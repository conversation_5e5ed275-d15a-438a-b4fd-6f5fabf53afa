﻿if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 4}"
	nm_Walk(31, Fwd<PERSON><PERSON>)
	nm_Walk(7.8, <PERSON><PERSON><PERSON>)
	nm_Walk(10, <PERSON><PERSON><PERSON>)
	nm_Walk(5, <PERSON><PERSON><PERSON>)
	nm_Walk(1.5, <PERSON>wd<PERSON><PERSON>)
	nm_Walk(60, <PERSON><PERSON><PERSON>)
	nm_Walk(3.75, <PERSON><PERSON><PERSON>)
	nm_Walk(85, Fwd<PERSON><PERSON>)
	nm_Walk(45, <PERSON><PERSON><PERSON>)
	nm_Walk(50, <PERSON><PERSON><PERSON>)
	nm_Walk(60, RightK<PERSON>)
	nm_Walk(15.75, <PERSON>wd<PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(18, FwdKey)
	send "{" RotRight " 4}"
	nm_Walk(31, <PERSON><PERSON><PERSON>)
	nm_Walk(3, <PERSON><PERSON><PERSON>)
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	send "{e down}"
	HyperSleep(100)
	send "{e up}"
	Sleep 4000
	nm_Walk(31, <PERSON><PERSON><PERSON>)
	nm_Walk(3, <PERSON><PERSON><PERSON>)
}
;path idkwhatimdoing money_mountain

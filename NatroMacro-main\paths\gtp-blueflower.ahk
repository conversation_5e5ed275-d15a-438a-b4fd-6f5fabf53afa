﻿if (MoveMethod = "walk" ) {
	nm_gotoramp()
	nm_Walk(88.875, <PERSON><PERSON><PERSON>, LeftKey)
	nm_Walk(27, <PERSON><PERSON><PERSON>)
	HyperSleep(50)
	send "{" RotLeft " 2}"
	nm_Walk(17, Fwd<PERSON><PERSON>) 
	nm_Walk(17, <PERSON><PERSON><PERSON>)
	nm_Walk(18, Fwd<PERSON><PERSON>)
	nm_Walk(10, <PERSON><PERSON><PERSON>)
	nm_Walk(7, <PERSON><PERSON><PERSON>, RightKey)
}
else {
	nm_gotoramp()
	nm_gotocannon()
	send "{e down}"
	HyperSleep(100)
	send "{e up}{" LeftKey " down}"
	HyperSleep(700)
	send "{space 2}"
	HyperSleep(4450)
	send "{" Left<PERSON><PERSON> " up}{space}"
	HyperSleep(1000)
	send "{" RotLeft " 2}"
	nm_Walk(19, <PERSON><PERSON><PERSON>)
	nm_Walk(18, FwdKey)
	nm_Walk(10, <PERSON><PERSON><PERSON>)
	nm_Walk(7, BackK<PERSON>, RightKey)
}
;path 230729 noobyguy

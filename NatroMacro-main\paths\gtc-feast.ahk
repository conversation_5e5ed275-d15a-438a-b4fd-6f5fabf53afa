﻿if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 4}"
	nm_Walk(31.5, Fwd<PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(58.5, <PERSON><PERSON><PERSON>)
	nm_Walk(49.5, <PERSON>w<PERSON><PERSON><PERSON>)
	nm_Walk(3.375, <PERSON><PERSON><PERSON>)
	nm_Walk(36, <PERSON>w<PERSON><PERSON><PERSON>)
	nm_Walk(60, <PERSON><PERSON><PERSON>)
	nm_Walk(60, <PERSON><PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(3.5, <PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(8.5, FwdKey)
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	send "{" RotLeft " 4}"
	HyperSleep(100)
	send "{e down}"
	HyperSleep(100)
	send "{e up}{" FwdKey " down}"
	HyperSleep(760)
	send "{space 2}"
	HyperSleep(2100)
	send "{" LeftKey " down}"
	HyperSleep(100)	
	send "{space}{" Fwd<PERSON><PERSON> " up}{" <PERSON><PERSON><PERSON> " up}"
	nm_Walk(10, <PERSON><PERSON><PERSON>)
	nm_Walk(6, <PERSON>w<PERSON><PERSON><PERSON>)	
	nm_Walk(2.2, <PERSON><PERSON><PERSON>)
	nm_Walk(2, <PERSON>wd<PERSON><PERSON>)
}
Send "{space down}"
HyperSleep(100)
Send "{space up}"
nm_Walk(5, FwdKey)
Sleep 1000

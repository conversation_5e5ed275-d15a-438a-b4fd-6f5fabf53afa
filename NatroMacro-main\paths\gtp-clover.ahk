﻿if (MoveMethod = "walk")
{
    nm_gotoramp()
    nm_Walk(44.75, <PERSON><PERSON><PERSON>, Left<PERSON>ey) ; 47.25
    nm_Walk(52.5, <PERSON><PERSON><PERSON>)
    nm_Walk(2.8, <PERSON><PERSON><PERSON>, RightKey)
    nm_Walk(6.7, <PERSON><PERSON><PERSON>) ; 6.7
    nm_Walk(25.5, <PERSON><PERSON><PERSON>)
    nm_Walk(35, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(7, <PERSON><PERSON><PERSON>, RightKey)
    nm_Walk(12, <PERSON><PERSON><PERSON>)
}
else
{
    nm_gotoramp()
    nm_gotocannon()
    send "{e down}"
    HyperSleep(100)
    send "{e up}{" LeftKey " down}{" FwdKey " down}"
    HyperSleep(525)
    send "{space 2}"
    HyperSleep(1250)
    send "{" FwdKey " up}"
    HyperSleep(3850)
    send "{" LeftKey " up}{space}"
    HyperSleep(1000)
    nm_Walk(10, Fwd<PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(15, <PERSON><PERSON><PERSON>)
    nm_Walk(7, <PERSON>wd<PERSON><PERSON>)
    nm_Walk(7, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(12, <PERSON><PERSON><PERSON>)
}
;path 230729 noobyguy

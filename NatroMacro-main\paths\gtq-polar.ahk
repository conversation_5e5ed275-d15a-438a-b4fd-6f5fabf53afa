﻿if (MoveMethod = "walk"){
	nm_gotoramp()
	nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 4}"
	nm_Walk(31.5, Fwd<PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(58.5, <PERSON><PERSON><PERSON>)
	nm_Walk(49.5, <PERSON>w<PERSON><PERSON><PERSON>)
	nm_Walk(3.375, <PERSON><PERSON>ey)
	nm_Walk(36, <PERSON>w<PERSON><PERSON><PERSON>)
	nm_Walk(60, <PERSON><PERSON><PERSON>)
	nm_Walk(60, <PERSON><PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(9, Fwd<PERSON><PERSON>)
}else{
	nm_gotoramp()
	nm_gotocannon()
	send "{" RotLeft " 4}"
	sleep 100
	send "{e down}"
	HyperSleep(100)
	send "{e up}{" FwdKey " down}"
	HyperSleep(800)
	send "{" FwdKey " up}{space 2}"
	HyperSleep(2100)
	send "{space}"
	sleep 1000
	nm_Walk(7, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>)
	nm_Walk(5, <PERSON><PERSON><PERSON><PERSON><PERSON>)	
}
nm_Walk(5, <PERSON><PERSON><PERSON>)
nm_Walk(2, <PERSON><PERSON><PERSON>)

﻿if (Hi<PERSON><PERSON><PERSON> > 25) {
    nm_gotoramp()
    Send "{space down}{" <PERSON><PERSON><PERSON> " down}"
    Sleep 100
    Send "{space up}"
    Walk(2)
    Send "{" Fwd<PERSON>ey " down}"
    Walk(1.8)
    Send "{" Fwd<PERSON><PERSON> " up}"
    Walk(30)
    send "{" <PERSON><PERSON><PERSON> " up}{space down}"
    HyperSleep(300)
    send "{space up}"
    nm_Walk(6, <PERSON><PERSON><PERSON>)
    HyperSleep(500)
    send "{" RotRight " 2}"
    send "{space down}"
    HyperSleep(100)
    send "{space up}"
    nm_Walk(3, FwdKey)
    HyperSleep(1000)
    send "{space down}{" RightKey " down}"
    HyperSleep(100)
    send "{space up}"
    HyperSleep(300)
    send "{space}{" RightKey " up}"
    HyperSleep(1000)
    nm_Walk(7.5, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)
}
else {
    nm_gotoramp()
	nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 4}"
	nm_Walk(31.5, <PERSON>wd<PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(58.5, <PERSON><PERSON><PERSON>)
	nm_Walk(49.5, <PERSON>wd<PERSON><PERSON>)
	nm_Walk(20.25, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 4}"
	nm_Walk(60.75, Fwd<PERSON>ey)
	send "{" RotRight " 2}"
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(15.75, BackKey, Right<PERSON>ey)
	nm_Walk(30, Left<PERSON>ey)
	nm_<PERSON>(36, Fwd<PERSON>ey)
	nm_Walk(28, Left<PERSON>ey)
	nm_Walk(5, Right<PERSON>ey)
	nm_Walk(3.5, Back<PERSON>ey)
	nm_Walk(23.5, Left<PERSON>ey)
	nm_Walk(3, Back<PERSON>ey)
	nm_Walk(10, RightKey)
	nm_Walk(3, LeftKey)
	nm_Walk(8, BackKey)
}
;dual path 230629 noobyguy

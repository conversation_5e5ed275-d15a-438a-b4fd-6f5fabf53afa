﻿nm_Walk(42, <PERSON><PERSON><PERSON>)
send "{" RotLeft " 4}"
nm_Walk(45, Fwd<PERSON><PERSON>)
nm_Walk(50, <PERSON><PERSON><PERSON>)
nm_Walk(49, Fwd<PERSON><PERSON>)
send "{" RotRight " 2}"
nm_Walk(13.5, FwdKey)
nm_Walk(1.5, <PERSON><PERSON><PERSON>) ;walk backwards to avoid thicker hives
nm_Walk(10, <PERSON><PERSON><PERSON>) ;walk to ramp
nm_Walk(2.7, Back<PERSON><PERSON>) ;center with hive pads

; [2024-01-15/rpertusio] Avoid using corner (Hive 1 and ramp) where character gets stuck after 2024-01-12 BSS update
; [2024-01-15/rpertusio] Updated camera angle to 'follow' user to hive, less disorienting

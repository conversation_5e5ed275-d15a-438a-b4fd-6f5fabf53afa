﻿send "{" RotRight " 2}"
nm_Walk(13.5, <PERSON>wd<PERSON><PERSON>)
nm_Walk(4.5, <PERSON><PERSON><PERSON>)
nm_Walk(46, <PERSON><PERSON><PERSON>) ; 40.5
nm_Walk(40.5, FwdKey)
nm_Walk(33.5, <PERSON><PERSON><PERSON>)
nm_Walk(27, <PERSON>w<PERSON><PERSON><PERSON>)

switch HiveSlot
    {
    case 3:
    nm_Walk(2.7, <PERSON><PERSON><PERSON>) ;center on hive pad 3

    default:
    nm_Walk(1.5, <PERSON><PERSON><PERSON>) ;walk backwards to avoid thicker hives
    nm_Walk(35, RightK<PERSON>) ;walk to ramp
    nm_Walk(2.7, BackK<PERSON>) ;center with hive pads
    }

; [2024-01-15/rpertusio] Avoid using corner (Hive 1 and ramp) where character gets stuck after 2024-01-12 BSS update
; [2024-01-15/rpertusio] Aligns with default SpawnLocation, saves walking if player chose Hive 3

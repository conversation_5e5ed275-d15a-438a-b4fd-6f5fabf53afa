﻿nm_Walk(16, <PERSON><PERSON><PERSON>)
nm_Walk(5, <PERSON><PERSON><PERSON>)
send "{" RotRight " 2}"
nm_Walk(75, <PERSON><PERSON><PERSON>)
nm_Walk(64, Fwd<PERSON>ey)
nm_Walk(5.5, <PERSON>wd<PERSON><PERSON>, <PERSON><PERSON><PERSON>)
nm_Walk(36, <PERSON>wd<PERSON><PERSON>)

switch HiveSlot
    {
    case 3:
    nm_Walk(2.7, <PERSON><PERSON><PERSON>) ;center on hive pad 3

    default:
    nm_Walk(1.5, <PERSON><PERSON><PERSON>) ;walk backwards to avoid thicker hives
    nm_Walk(35, <PERSON><PERSON><PERSON>) ;walk to ramp
    nm_Walk(2.7, <PERSON>K<PERSON>) ;center with hive pads
    }

;path 230212 zaappiix
; [2024-01-15/rpertusio] Avoid using corner (Hive 1 and ramp) where character gets stuck after 2024-01-12 BSS update
; [2024-01-15/rpertusio] Aligns with default SpawnLocation, saves walking if player chose Hive 3

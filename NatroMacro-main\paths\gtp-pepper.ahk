﻿nm_gotoramp()
Send "{space down}{" Right<PERSON><PERSON> " down}"
Sleep 100
Send "{space up}"
Walk(2)
Send "{" FwdKey " down}"
Walk(1.8)
Send "{" FwdKey " up}"
Walk(30)
send "{" RightKey " up}{space down}"
HyperSleep(300)
send "{space up}"
nm_Walk(4, <PERSON><PERSON><PERSON>)
nm_Walk(5, Fwd<PERSON><PERSON>)
nm_Walk(3, <PERSON><PERSON><PERSON>)
send "{space down}"
HyperSleep(300)
send "{space up}"
nm_Walk(6, FwdKey)
nm_Walk(2, Left<PERSON><PERSON>, Fwd<PERSON><PERSON>)
nm_Walk(8, FwdKey)
Send "{" FwdKey " down}{" RightKey " down}"
Walk(11)
send "{space down}{" RightKey " up}"
HyperSleep(200)
send "{space up}"
HyperSleep(1100)
send "{space down}"
HyperSleep(200)
send "{space up}"
Walk(18)
send "{space down}"
HyperSleep(200)
send "{space up}"
Walk(20)
Send "{" Right<PERSON><PERSON> " down}"
Walk(9)
send "{space down}"
HyperSleep(300)
send "{space up}"
Walk(1)
Send "{" FwdKey " up}"
Walk(33)
send "{space down}"
HyperSleep(300)
send "{space up}"
Walk(6)
send "{" RotRight " 2}"
nm_Walk(30, FwdKey)
Send "{" RightKey " up}"
nm_Walk(10, FwdKey)
nm_Walk(10, BackKey, LeftKey)
nm_Walk(4, BackKey)
;path 230212 zaappiix

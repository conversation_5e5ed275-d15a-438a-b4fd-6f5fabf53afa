﻿if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 4}"
	nm_Walk(31.5, Fwd<PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(58.5, <PERSON><PERSON><PERSON>)
	nm_Walk(49.5, <PERSON>w<PERSON><PERSON><PERSON>)
	nm_Walk(3.375, <PERSON><PERSON><PERSON>)
	nm_Walk(36, <PERSON>wd<PERSON><PERSON>)
	nm_Walk(54, <PERSON><PERSON><PERSON>)
	nm_Walk(54, <PERSON><PERSON><PERSON>)
	nm_Walk(58.5, <PERSON><PERSON><PERSON>)
	nm_Walk(3, <PERSON><PERSON><PERSON>)
	nm_Walk(57, Fwd<PERSON><PERSON>)
	nm_Walk(16, <PERSON><PERSON><PERSON>)
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	send "{e down}"
	HyperSleep(100)
	send "{e up}{" LeftK<PERSON> " down}{" BackKey " down}"
	HyperSleep(1400)
	send "{space 2}"
	HyperSleep(1100)
	send "{" LeftKey " up}"
	HyperSleep(650)
	send "{" BackKey " up}{space}{" RotRight " 4}"
	Sleep 1500
	nm_Walk(4, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>)
	nm_Walk(23, <PERSON>w<PERSON><PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
}
nm_Walk(3, <PERSON>wd<PERSON><PERSON>)
nm_Walk(8, <PERSON><PERSON><PERSON>)
nm_Walk(3.6, <PERSON><PERSON>ey)
nm_Walk(41, FwdKey)
send "{space down}"
HyperSleep(100)
send "{space up}"
nm_Walk(21, FwdKey)
send "{space down}"
HyperSleep(100)
send "{space up}"
nm_Walk(3, FwdKey)
Sleep 1000
;path 230212 zaappiix

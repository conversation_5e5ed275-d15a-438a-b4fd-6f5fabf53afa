﻿if (MoveMethod = "walk") 
{
    nm_gotoramp()
    nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    send "{" RotRight " 4}"
    nm_Walk(23.5, Fwd<PERSON><PERSON>)
    nm_Walk(31.5, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(10, <PERSON><PERSON><PERSON>)
    send "{" RotRight " 2}"
    nm_Walk(20, Fwd<PERSON><PERSON>)
    nm_Walk(5, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(7, <PERSON><PERSON><PERSON>)
    nm_Walk(1, FwdKey)
    nm_Walk(8, <PERSON><PERSON><PERSON>)
    nm_Walk(14, <PERSON><PERSON><PERSON>)
}
else {
    nm_gotoramp()
    nm_gotocannon()
    send "{" RotLeft " 2}{e down}"
    HyperSleep(100)
    send "{e up}{" FwdKey " down}"
    HyperSleep(800)
    send "{" SC_space " 2}"
    HyperSleep(3000)
    send "{" FwdKey " up}{" LeftKey " down}"
    HyperSleep(1000)
    send "{" SC_space "}"
    send "{" <PERSON><PERSON><PERSON> " up}"
    HyperSleep(1000)
    nm_Walk(20, <PERSON><PERSON><PERSON>)
    nm_Walk(30, <PERSON>w<PERSON><PERSON><PERSON>)
    nm_Walk(8, <PERSON><PERSON><PERSON>)
    nm_Walk(14, <PERSON><PERSON><PERSON>)
}
;path 230729 noob<PERSON><PERSON>y

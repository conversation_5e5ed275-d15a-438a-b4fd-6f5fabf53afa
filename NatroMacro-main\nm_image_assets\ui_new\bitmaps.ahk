; New Roblox UI 2024 bitmaps for reset functionality

; Reset confirmation dialog text - "Are you sure you want to reset your character?"
bitmaps["reset_dialog_text"] := Gdip_BitmapFromBase64("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==")

; Blue "Reset" button
bitmaps["reset_button_blue"] := Gdip_CreateBitmap(100, 40), G := Gdip_GraphicsFromImage(bitmaps["reset_button_blue"]), Gdip_GraphicsClear(G, 0xff4472c4), Gdip_DeleteGraphics(G)

; Dark "Don't Reset" button
bitmaps["dont_reset_button"] := Gdip_CreateBitmap(100, 40), G := Gdip_GraphicsFromImage(bitmaps["dont_reset_button"]), Gdip_GraphicsClear(G, 0xff2d2d2d), Gdip_DeleteGraphics(G)

; Hamburger menu icon (three horizontal lines)
bitmaps["hamburger_menu"] := Gdip_CreateBitmap(24, 24), G := Gdip_GraphicsFromImage(bitmaps["hamburger_menu"]), Gdip_GraphicsClear(G, 0xffffffff), Gdip_DeleteGraphics(G)
﻿if (MoveMethod = "Cannon") {
    nm_gotoramp()
    nm_gotocannon()
    send "{e down}"
    HyperSleep(100)
    send "{e up}{" RightKey " down}"
    HyperSleep(550)
    send "{space 2}"
    HyperSleep(2000)
    send "{" Right<PERSON>ey " up}"
    HyperSleep(1000)
    send "{space}{" RotRight " 2}"
    Sleep 1000
} else {
    nm_gotoramp()
    nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    send "{" RotRight " 4}"
    nm_Walk(31, Fwd<PERSON>ey)
    nm_Walk(7.8, Left<PERSON><PERSON>)
    nm_Walk(10, <PERSON><PERSON><PERSON>)
    nm_Walk(5, RightKey)
    nm_Walk(1.5, Fwd<PERSON>ey)
    nm_Walk(60, Left<PERSON><PERSON>)
    nm_Walk(3.75, <PERSON><PERSON><PERSON>)
    nm_Walk(38, Fwd<PERSON><PERSON>)
    send "{" RotLeft " 4}"
    nm_Walk(14, <PERSON>K<PERSON>)
    nm_Walk(15, Fwd<PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(1, <PERSON><PERSON><PERSON>)
    HyperSleep(200)
    nm_Walk(16, <PERSON><PERSON><PERSON>)
    nm_Walk(49, <PERSON>w<PERSON><PERSON><PERSON>)
    send "{" RotRight " 2}"
    ;path 230630 noobyguy
}

﻿if (MoveMethod = "walk")
{
    nm_gotoramp()
    nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    send "{" RotRight " 4}"
    nm_Walk(31, Fwd<PERSON><PERSON>)
    nm_Walk(7.8, <PERSON><PERSON><PERSON>)
    nm_Walk(10, <PERSON><PERSON><PERSON>)
    nm_Walk(5, <PERSON><PERSON><PERSON>)
    nm_Walk(1.5, <PERSON>w<PERSON><PERSON><PERSON>)
    nm_Walk(60, <PERSON><PERSON><PERSON>)
    nm_Walk(49.5, <PERSON>wd<PERSON><PERSON>)
    send "{" RotRight " 2}"
    nm_Walk(35.5, FwdKey)
    nm_Walk(3, <PERSON><PERSON><PERSON>)
    nm_Walk(7, <PERSON><PERSON><PERSON>)
    send "{" RotRight " 2}"
}
else {
    nm_gotoramp()
    nm_gotocannon()
    send "{e down}"
    HyperSleep(100)
    send "{e up}{" RightK<PERSON> " down}{" BackKey " down}"
    HyperSleep(890)
    send "{space 2}"
    HyperSleep(2500)
    send "{" RightKey " up}"
    HyperSleep(1100)
    send "{" BackKey " up}{space}{" RotLeft " 4}"
    HyperSleep(600)
    nm_Walk(15, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(22, <PERSON><PERSON><PERSON>)
    nm_Walk(30, <PERSON><PERSON><PERSON>)
    nm_Walk(7, <PERSON><PERSON><PERSON>)
    send "{" RotLeft " 4}"
}
;path 230729 noobyguy

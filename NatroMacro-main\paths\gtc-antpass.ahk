﻿nm_Walk(3, <PERSON>w<PERSON><PERSON><PERSON>)
nm_Walk(52, <PERSON><PERSON><PERSON>)
nm_Walk(3, <PERSON><PERSON><PERSON><PERSON><PERSON>)
send "{" Fwd<PERSON><PERSON> " down}{space down}"
HyperSleep(300)
send "{space up}"
nm_Walk(5, <PERSON><PERSON><PERSON>)
send "{space down}"
HyperSleep(300)
send "{space up}{" Fwd<PERSON>ey " up}"
HyperSleep(500)
nm_Walk(2, Fwd<PERSON><PERSON>)
nm_Walk(15, <PERSON><PERSON><PERSON>)
nm_Walk(6, <PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
nm_Walk(7, Fwd<PERSON><PERSON>)
nm_Walk(5, BackK<PERSON>, LeftKey)
nm_Walk(23, FwdKey)
nm_Walk(12, Left<PERSON><PERSON>)
nm_Walk(8, <PERSON><PERSON><PERSON>, <PERSON>wd<PERSON><PERSON>) ; 1.3
nm_Walk(10, FwdKey) ; 1.3
nm_Walk(5, <PERSON><PERSON><PERSON>) ; 1.3
nm_Walk(25, Fwd<PERSON><PERSON>, RightKey) ; 31
nm_Walk(25, LeftKey) ; 31
nm_Walk(17, <PERSON><PERSON><PERSON>)

﻿if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(88.875, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(27, <PERSON><PERSON><PERSON>)
	HyperSleep(50)
	send "{" RotLeft " 2}"
	HyperSleep(50)
	nm_Walk(50, Fwd<PERSON>ey)
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	send "{e down}"
	HyperSleep(100)
	send "{e up}{" LeftK<PERSON> " down}"
	HyperSleep(700)
	send "{space 2}"
	HyperSleep(4450)
	send "{" LeftKey " up}{space}"
	HyperSleep(1000)
	send "{" RotLeft " 2}"
	nm_Walk(4, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(8, <PERSON>wd<PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(6, FwdKey)
	nm_Walk(5, <PERSON><PERSON><PERSON>)
	nm_Walk(8, <PERSON><PERSON><PERSON>)
	;inside
	nm_Walk(30, FwdKey)
}
send "{space down}"
HyperSleep(100)
send "{space up}"
nm_Walk(6, Fwd<PERSON><PERSON>)
nm_Walk(5, <PERSON><PERSON><PERSON>)
nm_Walk(9, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
nm_Walk(4, <PERSON><PERSON><PERSON>)
nm_Walk(2, <PERSON><PERSON><PERSON>)
nm_Walk(28, <PERSON><PERSON><PERSON>)
nm_Walk(1.75, Fwd<PERSON><PERSON>)
nm_Walk(9.5, <PERSON><PERSON>ey)
nm_Walk(6.5, Fwd<PERSON>ey)
sleep 100
send "{space down}"
Hypersleep(300)
send "{space up}"
nm_Walk(5, FwdKey)
;path 230630 noobyguy

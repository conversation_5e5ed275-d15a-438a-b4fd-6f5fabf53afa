if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(69, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 4}"
	nm_Walk(30, Fwd<PERSON><PERSON>)
	nm_Walk(20, <PERSON>wd<PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 2}"
	nm_Walk(43.5, Fwd<PERSON><PERSON>)
	nm_Walk(16, <PERSON><PERSON><PERSON>)
	send "{" FwdKey " down}"
	HyperSleep(200)
	send "{space down}"
	HyperSleep(100)
	send "{space up}"
	HyperSleep(800)
	send "{" FwdKey " up}{" RotLeft " 2}"
	nm_Walk(29.25, FwdKey)
	nm_Walk(15, <PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(8, <PERSON><PERSON><PERSON>)
	nm_Walk(15, <PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(3.5, <PERSON><PERSON><PERSON>)
	nm_Walk(11, <PERSON><PERSON><PERSON>)
	send "{" RotLeft " 2}"
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	send "{e down}"
	HyperSleep(100)
	send "{e up}"
	HyperSleep(2500)
	nm_Walk(30, <PERSON>w<PERSON><PERSON><PERSON>)
	nm_Walk(2, <PERSON><PERSON><PERSON>)
	nm_Walk(22, <PERSON><PERSON><PERSON>)
	nm_Walk(12, <PERSON><PERSON><PERSON>)
	nm_Walk(3, <PERSON><PERSON><PERSON>)
	nm_Walk(5, Fwd<PERSON>ey)
	send "{" RotRight " 2}"
}
Sleep 1000
;path 230630 noobyguy - walk updated
;adjusted for memory match OAC

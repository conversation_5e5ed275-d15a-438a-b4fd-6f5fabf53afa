# Roblox Reset Function Fix for New UI (2024)

## Проблема
Після оновлення UI в Roblox у 2024 році, стара функція reset персонажа перестала працювати. Раніше використовувався метод `Esc -> R -> Enter`, але новий UI змінив розташування та поведінку меню.

## Рішення
Оновлено функції reset в двох файлах:

### 1. `lib\Roblox.ahk` - функція `nm_Reset()`
- Додано підтримку нового hamburger menu (меню з трьома лініями)
- Реалізовано множинні методи reset для сумісності
- Покращено позиціонування кліків для нового UI

### 2. `submacros\natro_macro.ahk` - основна функція reset
- Оновлено логіку reset в основному циклі макросу
- Додано функцію `nm_findResetDialog()` для пошуку діалогового вікна підтвердження
- Реалізовано fallback методи для максимальної сумісності

### 3. `nm_image_assets\ui_new\bitmaps.ahk` - нові зображення UI
- Додано bitmaps для нових елементів UI
- Підготовлено зображення для пошуку кнопок reset

## Нові методи reset

### Метод 1: Hamburger Menu (новий UI)
1. Клік на hamburger menu (зазвичай зліва вгорі)
2. Пошук опції "Respawn" або "Reset Character"
3. Клік на знайдену опцію
4. Підтвердження reset в діалоговому вікні

### Метод 2: Старий Escape метод (fallback)
1. Натискання Esc
2. Натискання R
3. Натискання Enter
4. Підтвердження в діалоговому вікні

### Метод 3: Альтернативні клавіатурні скорочення
1. Ctrl+R (якщо підтримується грою)
2. Інші можливі комбінації

## Функція `nm_findResetDialog()`
Ця нова функція:
- Шукає діалогове вікно підтвердження reset
- Знаходить синю кнопку "Reset" 
- Кліка на правильну позицію
- Перевіряє успішність reset

## Діалогове вікно reset
Нове діалогове вікно містить:
- Текст: "Are you sure you want to reset your character?"
- Синю кнопку "Reset" (зліва)
- Темну кнопку "Don't Reset" (справа)

## Тестування
Використовуйте `test_reset.ahk` для тестування:
```
F1 - тест функції reset
F2 - вихід
```

## Сумісність
- Працює з новим UI Roblox 2024
- Зберігає сумісність зі старим UI
- Автоматично перемикається між методами
- Підтримує різні розміри вікон та роздільності

## Примітки
- Функція автоматично визначає позицію вікна Roblox
- Використовує множинні спроби для надійності
- Включає перевірку успішності reset
- Очищає ресурси після використання

﻿if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 4}"
	nm_Walk(30, Fwd<PERSON><PERSON>)
	nm_Walk(20, <PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 2}"
	nm_Walk(43.5, Fwd<PERSON><PERSON>)
	nm_Walk(16, <PERSON><PERSON><PERSON>)
	send "{" Fwd<PERSON>ey " down}"
	HyperSleep(200)
	send "{space down}"
	HyperSleep(100)
	send "{space up}"
	HyperSleep(800)
	send "{" FwdKey " up}{" RotLeft " 2}"
	nm_Walk(29.25, Fwd<PERSON>ey)
	nm_Walk(17, <PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(3, Fwd<PERSON>ey)
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	send "{e down}"
	HyperSleep(100)
	send "{e up}{" LeftKey " down}"
	HyperSleep(1810)
	send "{space 2}"
	Hyper<PERSON>lee<PERSON>(1925)
	send "{" <PERSON><PERSON><PERSON> " up}{space}{" RotRight " 4}"
	Sleep 1500
}
;path 230630 noobyguy - walk updated

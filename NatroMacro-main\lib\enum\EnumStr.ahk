﻿static arr:=["webhook" ; 1
	, "bottoken" ; 2
	, "MainChannelID" ; 3
	, "ReportChannelID"
	, "discordUID"
	, "commandPrefix"
	, "MoveMethod"
	, "SprinklerType"
	, "ConvertBalloon"
	, "PrivServer"
	, "FieldName1"
	, "FieldName2"
	, "FieldName3"
	, "FieldPattern1"
	, "FieldPattern2"
	, "FieldPattern3"
	, "FieldPatternSize1"
	, "FieldPatternSize2"
	, "FieldPatternSize3"
	, "FieldReturnType1"
	, "FieldReturnType2"
	, "FieldReturnType3"
	, "FieldSprinklerLoc1"
	, "FieldSprinklerLoc2"
	, "FieldSprinklerLoc3"
	, "FieldRotateDirection1"
	, "FieldRotateDirection2"
	, "FieldRotateDirection3"
	, "MondoAction"
	, "AntPassAction"
	, "FieldBooster1"
	, "FieldBooster2"
	, "FieldBooster3"
	, "HotbarWhile2"
	, "HotbarWhile3"
	, "Hotbar<PERSON>hile4"
	, "HotbarWhile5"
	, "HotbarWhile6"
	, "HotbarWhile7"
	, "QuestGatherReturnBy"
	, "MoveSpeedNum"
	, "ReconnectInterval"
	, "ReconnectHour"
	, "ReconnectMin"
	, "FallbackServer1"
	, "FallbackServer2"
	, "FallbackServer3"
	, "NightAnnouncementName"
	, "NightAnnouncementPingID"
	, "NightAnnouncementWebhook"
	, "SnailTime"
	, "ChickTime"
	, "InputSnailHealth"
	, "InputChickHealth"
	, "ShrineItem1"
	, "ShrineItem2"
	, "ShrineIndex1"
	, "ShrineIndex2"
	, "BlenderIndex1"
	, "BlenderIndex2"
	, "BlenderIndex3"
	, "BlenderItem1"
	, "BlenderItem2"
	, "BlenderItem3"
	, "StickerStackItem"
	, "StickerPrinterEgg"
	, "MondoLootDirection"
	, "PlanterName1"
	, "PlanterName2"
	, "PlanterName3"
	, "PlanterField1"
	, "PlanterField2"
	, "PlanterField3"
	, "PlanterNectar1"
	, "PlanterNectar2"
	, "PlanterNectar3"
	, "PlanterHarvestFull1"
	, "PlanterHarvestFull2"
	, "PlanterHarvestFull3"]
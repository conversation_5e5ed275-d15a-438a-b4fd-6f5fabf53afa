﻿if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(47.25, <PERSON><PERSON><PERSON>, LeftKey)
	nm_Walk(40.5, <PERSON><PERSON><PERSON>)
	nm_Walk(8.5, <PERSON><PERSON><PERSON>)
	nm_Walk(43, <PERSON><PERSON><PERSON>)
	nm_Walk(13, Fwd<PERSON><PERSON>)
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	send "{e down}"
	HyperSleep(100)
	send "{e up}{" LeftKey " down}{" FwdKey " down}"
	HyperSleep(1180)
	send "{space 2}"
	HyperSleep(4950)
	send "{" FwdKey " up}{" Left<PERSON><PERSON> " up}{space}"
	Sleep 1500
}
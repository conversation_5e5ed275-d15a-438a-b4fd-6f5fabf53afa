if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 4}"
	nm_Walk(31.5, Fwd<PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(9, <PERSON><PERSON><PERSON>)
	nm_Walk(58.5, <PERSON><PERSON><PERSON>)
	nm_Walk(49.5, <PERSON>w<PERSON><PERSON><PERSON>)
	nm_Walk(3.375, <PERSON><PERSON><PERSON>)
	nm_Walk(36, <PERSON>w<PERSON><PERSON><PERSON>)
	nm_Walk(54, <PERSON><PERSON><PERSON>)
	nm_Walk(54, <PERSON><PERSON><PERSON>)
	nm_Walk(58.5, <PERSON><PERSON><PERSON>)
	nm_Walk(15.75, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(13.5, Fwd<PERSON>ey)
	send "{" RotRight " 4}"
	nm_Walk(27, <PERSON><PERSON><PERSON>)
	nm_Walk(18, <PERSON><PERSON><PERSON>)
	nm_Walk(27, <PERSON><PERSON><PERSON>)
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	send "{e down}"
	HyperSleep(100)
	send "{e up}"
	Sleep 3000
	nm_Walk(40.5, <PERSON>K<PERSON>)
}

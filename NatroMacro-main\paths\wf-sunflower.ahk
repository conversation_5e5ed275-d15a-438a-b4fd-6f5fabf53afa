﻿send "{" RotLeft " 2}"
nm_Walk(13.5, <PERSON><PERSON><PERSON>) ;walk to edge of field
nm_Walk(45, Fwd<PERSON><PERSON>)     ;walk to corner (special sprout)
nm_Walk(2.25, <PERSON><PERSON><PERSON>)  ;back out of corner
nm_Walk(25, <PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>) ;move diagonally towards hives
nm_Walk(13.5, Fwd<PERSON>ey)   ;walk towards hives
nm_Walk(1.5, <PERSON><PERSON><PERSON>) ;walk backwards to avoid thicker hives
nm_Walk(10, RightK<PERSON>) ;walk to ramp
nm_Walk(2.7, Back<PERSON><PERSON>) ;center with hive pads
; [2024-01-15/rpertusio] Avoid using corner (Hive 1 and ramp) where character gets stuck after 2024-01-12 BSS update

﻿nm_gotoramp()
Send "{space down}{" Right<PERSON><PERSON> " down}"
Sleep 100
Send "{space up}"
Walk(2)
Send "{" FwdKey " down}"
Walk(1.8)
Send "{" FwdKey " up}"
Walk(30)
send "{" RightKey " up}{space down}"
HyperSleep(300)
send "{space up}"
nm_Walk(4, <PERSON><PERSON><PERSON>)
nm_Walk(5, Fwd<PERSON><PERSON>)
nm_Walk(3, <PERSON><PERSON><PERSON>)
send "{space down}"
HyperSleep(300)
send "{space up}"
nm_Walk(6, FwdKey)
nm_Walk(2, <PERSON><PERSON><PERSON>, Fwd<PERSON><PERSON>)
nm_Walk(8, FwdKey)
Send "{" FwdKey " down}{" RightKey " down}"
Walk(11)
send "{space down}{" RightKey " up}"
HyperSleep(200)
send "{space up}"
HyperSleep(1100)
send "{space down}"
HyperSleep(200)
send "{space up}"
Walk(18)
send "{space down}"
HyperSleep(200)
send "{space up}"
Walk(20)
Send "{" Right<PERSON><PERSON> " down}"
Walk(9)
send "{space down}"
HyperSleep(300)
send "{space up}"
Walk(1)
Send "{" FwdKey " up}"
Walk(33)
send "{space down}"
HyperSleep(300)
send "{space up}"
Walk(4)
send "{" RightKey " up}{" FwdKey " up}{" RotRight " 2}"
nm_Walk(9, FwdKey)
nm_Walk(1.5, RightKey)
;path 230212 zaappiix

﻿if (MoveMethod = "Cannon") {
    nm_gotoramp()
    nm_gotocannon()
    send "{e down}"
    HyperSleep(100)
    send "{e up}{" RightKey " down}{" BackKey " down}"
    HyperSleep(700)
    send "{space 2}"
    HyperSleep(1700)
    send "{" RightKey " up}{" BackKey " up}{space}{" RotRight " 2}"
    Sleep 2000
} else {
    nm_gotoramp()
    nm_Walk(67.5, <PERSON><PERSON><PERSON>, Left<PERSON>ey)
    send "{" RotRight " 4}"
    nm_Walk(31, Fwd<PERSON><PERSON>)
    nm_Walk(7, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(33.25, <PERSON><PERSON>ey)
    nm_Walk(6.75, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    send "{" RotLeft " 2}"
    ;path 230630 nooby<PERSON>y adjusted line 7
}

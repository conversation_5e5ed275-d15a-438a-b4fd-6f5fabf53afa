﻿if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(44.75, <PERSON><PERSON><PERSON>, Left<PERSON><PERSON>) ; 47.25
	nm_Walk(28, <PERSON><PERSON><PERSON>)
	nm_Walk(10, <PERSON><PERSON><PERSON>)
	nm_Walk(6, <PERSON><PERSON><PERSON>)
	nm_Walk(12, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>) ;corner align
	sleep 500
	nm_Walk(1.5, <PERSON><PERSON><PERSON>)
	nm_Walk(12.2, Fwd<PERSON><PERSON>)
	nm_Walk(14, <PERSON><PERSON><PERSON>) ; ladder 1 ; 12.5
	nm_Walk(1.5, Right<PERSON><PERSON>, BackKey)
	nm_Walk(15, <PERSON><PERSON><PERSON>)
	nm_Walk(7.5, <PERSON><PERSON><PERSON>) ;corner align 2
	sleep 500
	nm_Walk(1.75, <PERSON>K<PERSON>)
	nm_Walk(4, FwdKey)
	nm_Walk(22.5, <PERSON><PERSON><PERSON>) ; ladder 2
	send "{" RotLeft " 2}"
	nm_Walk(40, FwdKey)
	nm_Walk(1, BackK<PERSON>)
	nm_Walk(20, RightKey) ; additional corner align for haste
	nm_Walk(5, Fwd<PERSON>ey)
	nm_Walk(16, <PERSON>K<PERSON>) ; 5
	nm_Walk(15, Fwd<PERSON>ey)
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	Send "{e down}"
	HyperSleep(100)
	Send "{e up}{" FwdKey " down}{" LeftKey " down}"
	HyperSleep(1500)
	send "{space 2}"
	Sleep 8000
	Send "{" Fwd<PERSON>ey " up}{" Left<PERSON>ey " up}"
	nm_Walk(15, Back<PERSON>ey)
	nm_Walk(3.5, Right<PERSON>ey)
	nm_Walk(2, RightKey, BackKey)
	nm_Walk(1, BackKey)
}

﻿nm_Walk(12, <PERSON>w<PERSON><PERSON><PERSON>)
nm_Walk(20, <PERSON><PERSON><PERSON>)
nm_Walk(8, <PERSON><PERSON><PERSON>)
send "{" RotLeft " 2}"
nm_Walk(35, <PERSON><PERSON><PERSON>)
nm_Walk(41, Fwd<PERSON><PERSON>)
nm_Walk(9, <PERSON><PERSON><PERSON>)
nm_Walk(28, <PERSON>w<PERSON><PERSON><PERSON>)
nm_Walk(8, <PERSON><PERSON><PERSON>)
nm_Walk(6, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
nm_Walk(6, Fwd<PERSON><PERSON>)
nm_Walk(1.5, <PERSON><PERSON><PERSON>) ;walk backwards to avoid thicker hives
nm_Walk(35, <PERSON><PERSON><PERSON>) ;walk to ramp
nm_Walk(2.7, BackK<PERSON>) ;center with hive pads

;path 230212 zaappiix
; [2024-01-15/rpertusio] Avoid using corner (Hive 1 and ramp) where character gets stuck after 2024-01-12 BSS update

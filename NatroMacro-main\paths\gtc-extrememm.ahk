nm_gotoramp()
Send "{space down}{" <PERSON><PERSON><PERSON> " down}"
Sleep 100
Send "{space up}"
Walk(2)
Send "{" Fwd<PERSON>ey " down}"
Walk(1.8)
Send "{" FwdKey " up}"
Walk(30)
send "{" Right<PERSON><PERSON> " up}{space down}"
HyperSleep(300)
send "{space up}"
nm_Walk(4, <PERSON><PERSON><PERSON>)
nm_Walk(5, <PERSON>wd<PERSON><PERSON>)
nm_Walk(3, <PERSON><PERSON><PERSON>)
send "{space down}"
HyperSleep(300)
send "{space up}"
nm_Walk(6, FwdKey)
nm_Walk(2, <PERSON><PERSON><PERSON>, <PERSON>wd<PERSON><PERSON>)
nm_Walk(8, FwdKey)
Send "{" FwdKey " down}{" RightKey " down}"
Walk(11)
send "{space down}{" RightKey " up}"
HyperSleep(200)
send "{space up}"
HyperSleep(1100)
send "{space down}"
HyperSleep(200)
send "{space up}"
Walk(18)
send "{space down}"
HyperSleep(200)
send "{space up}"
Walk(26)
Send "{" Fwd<PERSON><PERSON> " up}"
Send "{" <PERSON><PERSON><PERSON> " down}"
send "{space down}"
HyperSleep(200)
send "{space up}"
Walk(15)
Send "{" RightKey " up}"
nm_Walk(2, Fwd<PERSON><PERSON>)
Sleep 1000

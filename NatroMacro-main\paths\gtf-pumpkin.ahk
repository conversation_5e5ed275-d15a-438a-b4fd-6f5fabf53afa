﻿if (MoveMethod = "Cannon") {
    nm_gotoramp()
    nm_gotocannon()
    send "{e down}"
    HyperSleep(100)
    send "{e up}{" RightKey " down}{" <PERSON>Key " down}"
    HyperSleep(950)
    send "{space 2}"
    HyperSleep(2700)
    send "{" RightKey " up}"
    HyperSleep(500)
    send "{" BackKey " up}"
    HyperSleep(600)
    send "{space}{" RotLeft " 4}"
    Sleep 1500
} else {
    nm_gotoramp()
    nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    send "{" RotRight " 4}"
    nm_Walk(31, FwdKey)
    nm_Walk(7.8, <PERSON><PERSON><PERSON>)
    nm_Walk(10, <PERSON><PERSON><PERSON>)
    nm_Walk(5, RightKey)
    nm_Walk(1.5, Fwd<PERSON><PERSON>)
    nm_Walk(60, <PERSON><PERSON><PERSON>)
    nm_Walk(3.75, RightK<PERSON>)
    nm_Walk(38, FwdKey)
    nm_Walk(18, <PERSON><PERSON><PERSON>, <PERSON>wd<PERSON><PERSON>)
    nm_Walk(10, FwdKey)
    ;path 230630 noobyguy
}

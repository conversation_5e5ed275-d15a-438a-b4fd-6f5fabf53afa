﻿if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(88.875, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(27, <PERSON><PERSON><PERSON>)
	HyperSleep(50)
	send "{" RotLeft " 2}"
	;inside
	nm_Walk(50, Fwd<PERSON>ey)	
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	send "{e down}"
	HyperSleep(100)
	send "{e up}{" LeftKey " down}"
	HyperSleep(700)
	send "{space 2}"
	HyperSleep(4450)
	send "{" LeftKey " up}{space}"
	HyperSleep(1000)
	send "{" RotLeft " 2}"
	nm_Walk(4, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(8, <PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(6, FwdKey)
	nm_Walk(5, <PERSON><PERSON><PERSON>)
	nm_Walk(8, <PERSON><PERSON><PERSON>)
	;inside
	nm_Walk(30, FwdKey)
}
send "{space down}"
HyperSleep(100)
send "{space up}"
nm_Walk(6, Fwd<PERSON>ey)
nm_Walk(5, <PERSON><PERSON><PERSON>)
nm_Walk(9, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
nm_Walk(4, <PERSON><PERSON><PERSON>)
nm_Walk(2, <PERSON><PERSON><PERSON>)
nm_Walk(21, <PERSON><PERSON><PERSON>)
nm_Walk(3.4, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
nm_Walk(16, <PERSON><PERSON><PERSON>)
;path 230630 noobyguy

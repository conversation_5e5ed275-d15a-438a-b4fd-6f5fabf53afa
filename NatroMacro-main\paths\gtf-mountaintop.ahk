﻿if (MoveMethod = "Cannon") {
    nm_gotoramp()
    nm_gotocannon()
    send "{e down}"
    HyperSleep(100)
    send "{e up}{" LeftKey " down}{" BackKey " down}"
    HyperSleep(1525)
    send "{space 2}"
    HyperSleep(1100)
    send "{" Left<PERSON><PERSON> " up}"
    HyperSleep(350)
    send "{" BackKey " up}{space}"
    Sleep 1500
} else {
    nm_gotoramp()
    nm_Walk(67.5, Back<PERSON><PERSON>, LeftKey)
    send "{" RotRight " 4}"
    nm_Walk(31, Fwd<PERSON>ey)
    nm_Walk(7.8, <PERSON><PERSON><PERSON>)
    nm_Walk(10, BackKey)
    nm_Walk(5, RightK<PERSON>)
    nm_Walk(1.5, FwdKey)
    nm_Walk(60, LeftK<PERSON>)
    nm_Walk(3.75, <PERSON><PERSON><PERSON>)
    nm_Walk(85, FwdKey)
    nm_Walk(45, RightKey)
    nm_Walk(50, BackK<PERSON>)
    nm_Walk(60, <PERSON>K<PERSON>)
    nm_Walk(15.75, <PERSON>wd<PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(13.5, Fwd<PERSON><PERSON>)
    send "{" RotRight " 4}"
    ;path 230630 noobyguy -added corneralign and tweaked slightly 
}

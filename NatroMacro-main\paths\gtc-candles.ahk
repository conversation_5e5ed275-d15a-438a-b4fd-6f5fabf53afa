﻿nm_gotoramp()
Send "{space down}{" <PERSON><PERSON><PERSON> " down}"
Sleep 100
Send "{space up}"
Walk(2)
Send "{" Fwd<PERSON>ey " down}"
Walk(1.8)
Send "{" Fwd<PERSON>ey " up}"
Walk(30)
send "{space down}"
HyperSleep(300)
send "{space up}{" Fwd<PERSON><PERSON> " down}"
Walk(4)
send "{" Fwd<PERSON><PERSON> " up}"
Walk(3)
send "{" RightKey " up}{" RotRight " 2}"
Sleep 200
send "{space down}"
HyperSleep(100)
send "{space up}"
nm_Walk(3, FwdKey)
Sleep 1000
send "{space down}{" RightKey " down}"
HyperSleep(100)
send "{space up}"
HyperSleep(300)
send "{space}{" RightKey " up}"
HyperSleep(1000)
nm_Walk(4, RightK<PERSON>)
nm_Walk(14, FwdKey)
nm_Walk(8, RightK<PERSON>)
nm_Walk(5, LeftK<PERSON>)

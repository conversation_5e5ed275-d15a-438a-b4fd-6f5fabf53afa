﻿nm_Walk(9, <PERSON><PERSON><PERSON>)
send "{" RotLeft " 4}"
nm_<PERSON>(9, <PERSON><PERSON><PERSON>)
send "{" <PERSON><PERSON><PERSON> " down}"
send "{" <PERSON><PERSON><PERSON> " down}"
HyperSleep(2000)
send "{space down}"
HyperSleep(200)
send "{space up}"
HyperSleep(2000)
send "{" Back<PERSON>ey " up}"
send "{" LeftKey " up}"
nm_Walk(36, Fwd<PERSON><PERSON>)
nm_Walk(4.5, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
nm_Walk(4.5, <PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
nm_Walk(27, Fwd<PERSON><PERSON>)
nm_Walk(3, <PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
nm_Walk(85.5, Fwd<PERSON><PERSON>)

switch HiveSlot
    {
    case 3:
    nm_Walk(2.7, <PERSON><PERSON><PERSON>) ;center on hive pad 3

    default:
    nm_Walk(1.5, <PERSON><PERSON><PERSON>) ;walk backwards to avoid thicker hives
    nm_Walk(35, RightK<PERSON>) ;walk to ramp
    nm_Walk(2.7, Back<PERSON><PERSON>) ;center with hive pads
    }
; [2024-01-15/rpertusio] Avoid using corner (Hive 1 and ramp) where character gets stuck after 2024-01-12 BSS update
; [2024-01-15/rpertusio] Aligns with default SpawnLocation, saves walking if player chose Hive 3

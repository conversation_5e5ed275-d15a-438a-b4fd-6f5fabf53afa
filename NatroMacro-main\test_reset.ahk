#Requires AutoHotkey v2.0
#SingleInstance Force

; Test script for the new reset functionality
; This script will test the updated reset function

#Include "lib\Gdip_All.ahk"
#Include "lib\Gdip_ImageSearch.ahk"
#Include "lib\Roblox.ahk"

; Initialize GDI+
if !pToken := Gdip_Startup()
{
    MsgBox "Gdiplus failed to start. Please ensure you have gdiplus on your system"
    ExitApp
}

; Test the new reset function
F1::
{
    ; Test the updated nm_Reset function from lib\Roblox.ahk
    result := nm_Reset()
    if (result)
        MsgBox "Reset function executed successfully!"
    else
        MsgBox "Reset function failed!"
}

; Exit script
F2::ExitApp

; Cleanup on exit
OnExit((*) => Gdip_Shutdown(pToken))

MsgBox "Test script loaded!`n`nPress F1 to test reset function`nPress F2 to exit"

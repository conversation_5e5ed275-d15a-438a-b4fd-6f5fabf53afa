﻿if (MoveMethod = "walk")
{
    nm_gotoramp()
    nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    send "{" RotRight " 4}"
    nm_Walk(31, Fwd<PERSON><PERSON>)
    nm_Walk(7.8, Left<PERSON><PERSON>)
    nm_Walk(10, <PERSON><PERSON><PERSON>)
    nm_Walk(5, <PERSON><PERSON><PERSON>)
    nm_Walk(1.5, Fwd<PERSON><PERSON>)
    nm_Walk(60, <PERSON><PERSON><PERSON>)
    nm_Walk(3.75, <PERSON><PERSON><PERSON>)
    nm_Walk(38, Fwd<PERSON><PERSON>)
    send "{" RotLeft " 4}"
    nm_Walk(14, <PERSON><PERSON><PERSON>)
    nm_Walk(15, <PERSON>wd<PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(1, <PERSON><PERSON><PERSON>)
    HyperSleep(200)
    nm_Walk(16, <PERSON><PERSON><PERSON>)
    nm_Walk(49, Fwd<PERSON>ey)
    send "{" RotLeft " 4}"
    nm_Walk(10, <PERSON><PERSON><PERSON>)
    nm_Walk(12, <PERSON><PERSON><PERSON>, Fwd<PERSON><PERSON>)
    nm_Walk(7, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    send "{" RotLeft " 2}"
}
else {
    nm_gotoramp()
    nm_gotocannon()
    send "{e down}"
    HyperSleep(100)
    send "{e up}{" <PERSON><PERSON><PERSON> " down}"
    HyperSleep(550)
    send "{space 2}"
    HyperSleep(2500)
    send "{" RightKey " up}{space}{" RotLeft " 4}"
    HyperSleep(1000)
    nm_Walk(17, Fwd<PERSON>ey)
    nm_Walk(10, Right<PERSON>ey)
    nm_Walk(8, Fwd<PERSON>ey, Right<PERSON>ey)
    nm_Walk(8, Fwd<PERSON>ey)
    nm_Walk(7, Back<PERSON>ey, Left<PERSON>ey)
    send "{" RotLeft " 2}"
}
;path 230729 noobyguy

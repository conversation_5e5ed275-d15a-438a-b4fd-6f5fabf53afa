﻿if (MoveMethod = "walk") {
    nm_gotoramp()
    nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    send "{" RotRight " 4}"
    nm_Walk(31, Fwd<PERSON><PERSON>)
    nm_Walk(7, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(30.25, <PERSON><PERSON><PERSON>)
    nm_Walk(30, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    send "{" RotLeft " 2}"
    nm_Walk(10, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
}
else {
    nm_gotoramp()
    nm_gotocannon()
    send "{e down}"
    HyperSleep(100)
    send "{e up}{" RightKey " down}{" BackKey " down}"
    HyperSleep(750)
    send "{space 2}"
    HyperSleep(1000)
    send "{" RightKey " up}{" BackKey " up}"
    HyperSleep(800)
    send "{space}{" RotRight " 2}"
    Sleep 2000
    nm_Walk(10, Fwd<PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(15, <PERSON><PERSON><PERSON>)
    nm_Walk(15, FwdKey)
    nm_Walk(10, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
}
;path 230729 noobyguy

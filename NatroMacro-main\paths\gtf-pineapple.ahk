﻿if (MoveMethod = "Cannon") {
    nm_gotoramp()
    nm_gotocannon()
    send "{e down}"
    HyperSleep(100)
    send "{e up}{" <PERSON><PERSON>ey " down}"
    Hyper<PERSON>leep(1850)
    send "{space 2}"
    HyperSleep(2750)
    send "{" <PERSON><PERSON><PERSON> " up}{" <PERSON><PERSON>ey " down}"
    HyperSleep(1150)
    send "{" BackKey " up}{space}{" RotRight " 4}"
    Sleep 2000
} else {
    nm_gotoramp()
	nm_Walk(67.5, <PERSON><PERSON><PERSON>, LeftKey)
	send "{" RotRight " 4}"
	nm_Walk(30, FwdKey)
	nm_Walk(20, Fwd<PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	send "{" RotRight " 2}"
	nm_Walk(43.5, Fwd<PERSON>ey)
	nm_Walk(18, <PERSON><PERSON><PERSON>)
	nm_Walk(6, Fwd<PERSON><PERSON>)
	send "{" RotLeft " 2}"
	nm_Walk(65.5, FwdKey)
    nm_Walk(1.5, RightKey)
    ;path 230630 noobyguy
}

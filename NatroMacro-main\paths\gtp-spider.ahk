﻿if (MoveMethod = "walk") {
    nm_gotoramp()
    nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    send "{" RotRight " 4}"
    nm_Walk(37.5, Fwd<PERSON>ey)
    nm_Walk(38, <PERSON><PERSON><PERSON>, <PERSON>wd<PERSON><PERSON>)
    nm_Walk(9, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
}
else {
    nm_gotoramp()
    nm_gotocannon()
    send "{e down}"
    HyperSleep(100)
    send "{e up}{" BackKey " down}"
    HyperSleep(1050)
    send "{space 2}"
    HyperSleep(300)
    send "{" BackKey " up}{space}{" RotLeft " 4}"
    Sleep 1500
    nm_Walk(20, FwdKey)
    nm_Walk(10, <PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(10, Left<PERSON><PERSON>)
    nm_Walk(9, <PERSON><PERSON><PERSON>, RightKey)
}
;path 230729 noobyguy

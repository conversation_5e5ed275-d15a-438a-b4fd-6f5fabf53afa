﻿nm_gotoramp()
Send "{space down}{" <PERSON><PERSON><PERSON> " down}"
Sleep 100
Send "{space up}"
Walk(2)
Send "{" Fwd<PERSON>ey " down}"
Walk(1.8)
Send "{" FwdKey " up}"
Walk(30)
send "{" Right<PERSON><PERSON> " up}{space down}"
HyperSleep(300)
send "{space up}"
nm_Walk(4, <PERSON><PERSON><PERSON>)
nm_Walk(5, <PERSON>wd<PERSON><PERSON>)
nm_Walk(3, <PERSON><PERSON><PERSON>)
send "{space down}"
HyperSleep(300)
send "{space up}"
nm_Walk(5, FwdKey)
nm_Walk(2, <PERSON><PERSON><PERSON>, <PERSON>wd<PERSON><PERSON>)
nm_Walk(8, FwdKey)
Send "{" FwdKey " down}{" RightKey " down}"
Walk(11)
send "{space down}{" RightKey " up}"
HyperSleep(200)
send "{space up}"
HyperSleep(1100)
send "{space down}"
HyperSleep(200)
send "{space up}"
Walk(18)
Send "{" FwdKey " up}"
nm_Walk(7, <PERSON><PERSON><PERSON>)
;path 230212 zaappiix

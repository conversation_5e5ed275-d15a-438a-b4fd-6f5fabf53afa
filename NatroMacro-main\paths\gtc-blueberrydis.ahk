﻿if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(88.875, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(27, <PERSON><PERSON><PERSON>)
	HyperSleep(50)
	send "{" RotLeft " 2}"
	HyperSleep(50)
	nm_Walk(30, Fwd<PERSON><PERSON>)
	nm_Walk(11.5, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(2, <PERSON><PERSON><PERSON>)
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	send "{e down}"
	HyperSleep(100)
	send "{e up}{" LeftKey " down}"
	HyperSleep(700)
	send "{space 2}"
	HyperSleep(4450)
	send "{" LeftKey " up}{space}"
	HyperSleep(1000)
	send "{" RotLeft " 2}"
	nm_Walk(10, Left<PERSON><PERSON>)
	nm_Walk(8, <PERSON><PERSON><PERSON>)
	;inside
	nm_Walk(10, FwdKey)
	send "{" RotRight " 1}"
	HyperSleep(100)
	nm_Walk(1.6, FwdKey)
	send "{FwdKey down}{space down}"
	HyperSleep(300)
	send "{space up}"
	send "{space}"
	<PERSON><PERSON><PERSON><PERSON><PERSON>(1300)
}
;path 230629 noobyguy
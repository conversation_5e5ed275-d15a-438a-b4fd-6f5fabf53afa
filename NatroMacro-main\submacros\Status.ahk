﻿/*
Natro Macro (https://github.com/NatroTeam/NatroMacro)
Copyright © Natro Team (https://github.com/NatroTeam)

This file is part of Natro Macro. Our source code will always be open and available.

Natro Macro is free software: you can redistribute it and/or modify it under the terms of the GNU General Public License as published by the Free Software Foundation, either version 3 of the License, or (at your option) any later version.

Natro Macro is distributed in the hope that it will be useful. This does not give you the right to steal sections from our code, distribute it under your own name, then slander the macro.

You should have received a copy of the license along with Natro Macro. If not, please redownload from an official source.
*/

#NoTrayIcon
#SingleInstance Force
#MaxThreads 255
#Warn VarUnset, Off

#Include "%A_ScriptDir%\..\lib"
#Include "Gdip_All.ahk"
#Include "Gdip_ImageSearch.ahk"
#Include "JSON.ahk"
#Include "DurationFromSeconds.ahk"
#Include "Roblox.ahk"

OnError (e, mode) => (mode = "Return") ? -1 : 0
SetWorkingDir A_ScriptDir "\.."
CoordMode "Mouse", "Client"

if (A_Args.Length = 0)
{
	MsgBox "This script needs to be run by Natro Macro! You are not supposed to run it manually."
	ExitApp
}

; initialisation
MacroState := PublicJoined := logsize := HoneyUpdate := 0
status_buffer := [], command_buffer := []

discordMode := A_Args[1]
discordCheck := A_Args[2]

webhook := A_Args[3]
bottoken := A_Args[4]

MainChannelCheck := A_Args[5]
MainChannelID := A_Args[6]
ReportChannelCheck := A_Args[7]
ReportChannelID := A_Args[8]
WebhookEasterEgg := A_Args[9]

ssCheck := A_Args[10]
ssDebugging := A_Args[11]
CriticalSSCheck := A_Args[12]
AmuletSSCheck := A_Args[13]
MachineSSCheck := A_Args[14]
BalloonSSCheck := A_Args[15]
ViciousSSCheck := A_Args[16]
DeathSSCheck := A_Args[17]
PlanterSSCheck := A_Args[18]
HoneySSCheck := A_Args[19]

criticalCheck := A_Args[20]
discordUID := A_Args[21]
CriticalErrorPingCheck := A_Args[22]
DisconnectPingCheck := A_Args[23]
GameFrozenPingCheck := A_Args[24]
PhantomPingCheck := A_Args[25]
UnexpectedDeathPingCheck := A_Args[26]
EmergencyBalloonPingCheck := A_Args[27]

commandPrefix := A_Args[28]

NightAnnouncementCheck := A_Args[29]
NightAnnouncementName := A_Args[30]
NightAnnouncementPingID := A_Args[31]
NightAnnouncementWebhook := A_Args[32]
PrivServer := A_Args[33]

DebugLogEnabled := A_Args[34]

MonsterRespawnTime := A_Args[35]

HoneyUpdateSSCheck := A_Args[36]

pToken := Gdip_Startup()
OnExit(ExitFunc)
OnMessage(0x004A, nm_sendPostData, 255)
OnMessage(0xC2, nm_setStatus, 255)
OnMessage(0x5552, nm_setGlobalInt, 255)
OnMessage(0x5553, nm_setGlobalStr, 255)
OnMessage(0x5556, nm_sendHeartbeat)
OnMessage(0x5559, nm_sendItemPicture)

discord.SendEmbed("Connected to Discord!", 5066239)

planters := Map(), planters.CaseSense := 0
planters["blueclayplanter"] := {bitmap: Gdip_BitmapFromBase64("iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAB2lBMVEUAAAAeHh42NjY1NTUyMjIoKCgkJCQaGho1NTU2NjYzMzM0NDQzMzMvLy8tLS0qKio2NjY2NjYzMzM0NDQxMTEuLi4uLi4qKio2NjY1NTU0NDQsLCwvLy8kJCQyMjIzMzMzMzMzMzMyMjI0NDQ1NTUyMjIyMjIxMTEvLy8wMDEtLS0tLS05WbpAZs8/UH82NjY2NjdAZs5NcddAZcw4WLZOctg2N0I1NjY5WrsyTqE4WLg3V7QyT6Q5Wrg1VLE/UIJMb9Y9Ycc6W70tSJU+UYRMb9NFadFCZ9BBZ85Mbco4VbEzUKc3UKYxTZ4sRpM3O0w1Nz01NztJbdVFaM1KacJBYLo2VrMwTKAuSpo/VJE3TJFAU403SYo/Uoc4R3s3RHA6QmQ3P2A2PVc2O1E2NjlNb9BHactBZMlGZ8ZFZL40VK40U6w3U6szUqpAWaQ2Tp88U5wwTJw1SY03Qmw2QGk2OkdGa9NHa9JJa88+ZMpKasg6XcBEYbU8W643VK1EXqo4U6Y3UaItSJc6To41RYs/Uok3RXg3PVtEZsJAYcI/YL4+XbdBXbFGYK9EXKM2TZo/VplBVpY3TJU7TIo7S4c4SIIyO1xKbM9CZMY8X8I4Va49UJIuQ4U1QYKebRTcAAAALHRSTlMABP33xTITCfH73sqGY0Ia6uK7tXtMLB3awKxGOyWwpZmTb9fQzoBsW1lZI7SYQXAAAANuSURBVDjLfdVnV9pQGMDxECxiAdFau/ceofdmEPaSpSAbyl6Ce++9q3bvPb5rb5KCKNj/i5wc7i95yD05J1i1ZuUjheJGE5dMDABoa2q6pLjcjB3rqvyOVAMAhGwgECj1FQulAAsBaGm/eUVUwx7fbAOh+YLjw9s30R5Uf79z6MWrqc/5+TIUXzpfdecuQPbJmxUnRanVFGV19vesDD1/8Z2m6ZdTsyHQpBSY6NQFWFhDxvptYGj49cT79J7DYbE4ssmJWPfC4BQLLgr/9JYUFqKUMxKbSO45LE9qs2xSVOQTFPPDr0qDuRVrZDOJTF3ZAbUzNo8rOHgK7EeskYknDUsuqRcHD8BZHmo+dbvpZEOXfq6m3HShAr/QKvK1o05Z9t5GKcqlomfBPQHmaZWKjL1PZy0CcGSz6fSHzWg/2ioXqSJzQCbiYXFQhepe6okOD6+txYaHBgaWnJSasi64uAXyMzzNwXMtgZc8DFspCi2r+ajwoptEd+PgrgCbb7OvOIio2+1aCIfDiy6Xu1t12C8BYnfL66qGkSqhXY0AZXCXbKyE6DwQ4Fm8OPgfSK6zsAPjOi8OTdHkSdOXuw6guJWHzTLIrp8EbV0eFl5vrr6O+zYVeYygaNuqltgKwU7sX514ybBqs9F8JIkOy8s2249VrZYgiMlyS2sFSqTBbcJEaLXaLi5unSNCKdimrEBRB8gQJ/R0JoTfxyopYF5fb7ifGKP9K7hWha0tfRtmhl98ahZOmFGj0WvWj0/7v4KOKryM9037vHo0yDid8BnN6ILxhN/u03l99t8suH54Rzw4s7MzQph1dr8/odONGrwJf3x0XKfz2UsacWcVSmQgN2JPGOM7du+IwaxDwB9n0IDRke2gRi7Camcbxqd18biZf1Kv0WhEjusdxK9ihymlwS1Cb2CIujKgXVIDRWfgDNGwHDiD1SYHc3pTA8fMHYOdOLtNHMZfM+Yhenv7wI0jUHIRzDFEr8FEML0MQuhgmpkkNj4GwQPsSK3i0EfTs0kt4fnjQROf/UR4TD9W1IivHIWia+DAMDbJGY8J7YoBnW1kShrNJRF2tHNt5RSDCMFvEuPZSs3OQ4CfkWDHkwN2bnb/SyaVyuTyxQBb1kC8/Wy9w863AwAgH+BqEV98qMQadaoDB5Wk8ltXJLXfjr8sUSDBBtdhwQAAAABJRU5ErkJggg==")
	, name: "Blue Clay Planter", color: 5403341}
planters["candyplanter"] := {bitmap: Gdip_BitmapFromBase64("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")
	, name: "Candy Planter", color: 12822478}
planters["festiveplanter"] := {bitmap: Gdip_BitmapFromBase64("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")
	, name: "Festive Planter", color: 12368237}
planters["heattreatedplanter"] := {bitmap: Gdip_BitmapFromBase64("iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAC+lBMVEUAAABCKiY4NTU1NjYjIyQTExMZGRk9NjSERx5JIRyNSho+LiyVShg3ODgsLi4xMzMjIiIlJCQQEBBHNzBQOy5PMi5bPSuOKRguLCs0MzMxNTU5ODgtLi45ODgsKyshIB8qKikxMTEbGxseHh4bGxsnJycgICAeHh4UFBQICAQNDQ0PDw8wMTFUPC0sKChsLyZOKSQkHh1rIhZMMi9eMylmQSiFLB2LKxtbHxYmIyJXKiA4ODgwMzU5ODgzNDQ4ODctLSwxMTE3NzUzMzM1NTUmJiYsLCwXFxcYGBg7MTBwQyR4RCKUQRmCOxMfEQ9qNyYgIiN5LyJyKx+hURZkQCd1LiOARx4xNjYuMTM3NjYxMDA3NjY3Nzc2NTUlJyc4ODgyMjIPDw8XERFYGA0YERBWFw52IBE0Nzg2NjYUEBAPDhBbGQ5VGA1TFg0yNTanQBGpThBwHRBpHA83Ojo2OTmmTRFnGg5gHQ1FEgo6OTk4OTkyNzmdLBWUKBWiMxOmUBGiShF6IBFfFwxVFgxBEAk1DQgyDQg0ODk3ODhDNjKQJhV9IRKkPBF0HhGrTRBtHBBsHA9cFgxXFgwKBwgwNjiZKhWSKRWNJxSVMBKBIhKvURFyHxGpURCoRhASDhBiGQ5fGQ5aFwxSEwwvNDc0NTafLxWaKxWgMROGKBOLJROFJBOoSBKJLBIcFBKqPxGGIhEQDRGsURCtRBAWDxCDNw9xKQ9kGw4sEQ5sJw0UDQ1LFAo8EAkEAwQ/OTc7NDVGJiJrIROhQxJQGxKwSxGpSxGQQRAbERCNQA94Hg9OFg5bGw1dHQwOCww3NDRXMixmMSiRSxkwHRciGReeThSPJRSIIxOhPhKjNxJUKxJ1JBKdQxGXPhGQOhGkOBFpMxFnMBFIGBGtThCoPRB1KBBeKBBXIxAmExBPIg8dDw92MA1yLA1nJA1LPDKKQRwYGBxCKhuXTRiDMBNlHxO1URKrORJzMhJHIBGURRCJNRByHRA6FBCYSA94MA5lIg1wTbUoAAAAYnRSTlMA/f75jEMj/v7+/v378dDHoWgR/v7+/v798ezn18+ThYF4dnBZSDouJhUNBv7+/v7+/v79/f39/f38+/bl49rWvaygmIBPNBwK/v7+/v7+/f39/f38/Pvx7uvKtaalmIxgM1lYq/cAAAQoSURBVDjLddJVUBtRFIDhTdIWKAXq7u7u7u7ukpuNuwcCIYQSJ0Jxd3eX4g51d3d3n2lCH8iG9H/Y2Ydvzp1750AWje8xctSKwc7OA0+Zc1vqvGrUqJFjIWTr16xy2Sd3kxPtlL1mnT037dzZPko0ncvhOK1BuLVD5G52fWZP2zF3Xr9+quTMzCuX3fvN3TkdhTqzZL2lG0Tvs2dewOXMRr2XLKbEu7S0VFgiizbEyKJRnM4WsDt3oXsmCwhLT/r4+Jw052P+8dbpjCjOaEvoOp9NbRJ6e3sLhULTp4RmNOjVKYEpBTDKaTwCOhgBoAAAWOrgpOTAAPeAwBS8LpIKCxwXT7SAKxQLDQDQgLkodiiLXRAFYCoVwII65cCe7a7n4tdbZIDymwKbINwCaDAMZHmvaJSYl7uULpPa4YT9U3hFMQJJnsxEYuoajDCgCSR1Auorz26io5DFxIHlm5lFLyS8hqIWQZ4krOFPc/632myJZ5jnbE53yKIj9FlvJNk5n2tCeJKQjIyQbF5IalBa3t3ryjhRD0s4bhEh7t3jyo+CJ/c+JN2orPxy9/H9p/XFfm8JmBEQorEHXbe3kk1l5daTyBFZWWQyCevnt8DVBbJqtKh/+kWcKSwWi/uXCU4VLbeGXTCTZ1xIz8ValFvW2t91uDWc5EKccRHXrnBYHKksYrLraMi6oaIFxRbjIrDY4rJ0u7iV1u4YRt6/TbSdTkr/isO2lj2zpyvGIN34uF69p5qOi7hwz3yZrOtVhaRiv0c1U+iHkHCM28bUmc9J9eQnn56RcaTnt2uqyaSnATld0U5dEHCYfG+O/30SNiI/JOgnjtR0jZlWuJUifnhJSeiMgJ1EXTNU7EJc4XffRFaJ8Eci7yqfpvO/w+stt4Knu2W4s+Em461UsZYCWAnhqaE0lv81375uHeBDMR6AyKpwfy2gst01zBs0aYJpYkfIiE8uoPE9Ge6hAI664sHMj04RM86jMSeQ0NU+3MNfCtg8hkoPAEUazyziqzx858QOgRANd9v9QBOv0heYJvKpgMoS175kJTDe2ylM64hcSHRXXw9xMP9muFgKANAmMvPV5oHLJkLIVtJ7XQr3CNDfZnqo2HB0MOOXINn03MQRHdZsSWzfMEa89lEYIwEPogJ9XxiCmH1jB3WBrBujoG/K1gR73WQmqkFkkGdLpOY8mngc6thqItr+QaAsLUyjBlFBns18jT2duNoGnLSMvq0qCcbXXtVSQdqtZiBVTycMmGBDOpfPlOLxjUnBbAolVKrT4xvnEwesswEHl1MNkXxdKL4tLYsfjeLanDhY4VhRDRsNXuYooNrBEUM8DNlohIhLOOOIQqEqHBwqUBvQBK5iyDrIVp2HLsVwOBwuwRSXizmwfHhP6D9NHDty2LChTkTioE49xiHf+i9gfNyMS7+nhQAAAABJRU5ErkJggg==")
	, name: "Heat-Treated Planter", color: 10172428}
planters["hydroponicplanter"] := {bitmap: Gdip_BitmapFromBase64("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")
	, name: "Hydroponic Planter", color: 9812161}
planters["paperplanter"] := {bitmap: Gdip_BitmapFromBase64("iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAB+1BMVEUAAAA2NTUhISElJSUkJCQ1NTU1NTUcHBw2NjY0NDQwMDAzMzMwMTEvLy81NTU0NDQyMjI0NDQzMzMYGBg2NjY0NDQzMzM0NDQzMzMxMTEvLy8yMjIvLy81NTU1NTU1NTU0NDMxMjIwMDAuLi4uLi4mJiYkJCQ1NTU1NzYwMC8wMDDRqmO6m13PqWPMqGK6m143NjbDoF+4mlw8OTbFoV/Cn126m1y2mFusj1bOqWPOqGK7m1y1lluafknJpWC4mVypjFSnilOliFFAPDaukVl0uyjLp2Gxk1mojFaOdUU5ODbHomDBnl+7nF6+nFylild3ZUaMc0WUbT6Tajw1NjTBnly3llyylVqqjVerjVShg1CHcUt7aUmNdUeeeUZzYkNlWD5jVT2PaDtWSztKRDh8XTdxtyhqpydopibVr2nSq2TRq2TLp2K3mmG/nWDIo1+iiFSfhVKfgUyMdEyDcEuddUSGb0RuXkJqXEGCXziBzTJjmS1rridpqiasjVScgVKWflGafEiYdESDbUOPdD2SaDxeUjtRRztNRThHQThEPziBmTeLfzeH1TV5pTJTezBqpi12wCpwtSamjl62kFWNeU6gfUqNa0KGhT+FhTY7RTZ7qjROcjFLaTFoozBYhzBIZzB3lS98qy54nC5Zhi55xC14ni10uClnpiiaiWq0AAAAK3RSTlMA+xIhCf3zBffIdW9kP+O7tqWVDOqtoaB9aF47M+bZzsFXUTcrGRbr0IRzNB0GUAAAA0xJREFUOMuF1HdTGkEYBnAOETGiRo0ajek9LFwHjl4FFBAEe+8l9hZj7z229N7Lx8zdrsAZx/D8czPcb95375kdJP8kLSUXi8pzZZIkkRVg0Y+zX7CbyVwOcfB5du7wXX4SeBs7mPnZPdfz4WqS3ZnRT89m3Me9P7CHykyZVJp23uZC4pCH3T2zdBmtuK5QZF+5VVx85zTKk94qSCc23h8d8XDfN9W05WxtLcMIJ30pU+yU9xUYTbf5dt1u91zvvtlsnmoym1dbxgcrdq7L4uXJihRbbR1TTU3hN7/d3fO9u8vLZj5jC3V+ozaUmhJzhXKsM+zz+cLh8Nq34/k/PXuRyvaVV2NDRi0AoDEOM7HG54MLvvqVykgksvZ1vvd7ZfvT1wFrtRHwwVuu5Z3AImKwr089zL3g3z/t6nr7a299fbJaqwVCdPaO9NgRS1JH+/tVKpWmqo9pnmyOtHd11Q8hRukd+u3cGLyHjfc/VsGoOTXZXDnZPCQoq8fucFm99IV4hVenObUqFo61WEAABx6Hw6WjKGqEeBQvsZB2kSTJlMasBsdr7HpXDdy+lJoovARr4F8bLCRLlnIC1Nn0VlxvgrABtoMixSbQKIYpZ/1+MqjXaQFeR0E4LobyEDpjFXyoSQHE4BJRnLj/N6YN8GwsGuyH0IZgBfpqlItP4ChLOXQGI4JWCF3Os5AZEEEAP0YLvGVi2AIhqUHtQId7PGi1COZlNUJBok8qRdBhh8/FqFJUTwMUrBiavEhOiOq5g43AlUbxapuOqvcEAAjJ78ahktAL9fktbALq7IGAacwKqNZ0STxFdDXvWI2qSgOhloe1JqGhUauuUwTznQx0j8uHUT3CQC3cX+915iRgducAxwrDuKD6BNaZAEztKqEUwQ4La4BXt5wRVgsDAYqrVZ4i6nunmju54VUWCPU1iDVsY5fTEjAD21zUIGkIGgTowIUjjmwSWTeRQ0nLuEaETCdtB9UWoPPiAHganZcypJLTkRbI6SdehhOOSQ7DzaMdqelXJGdTkiOPdoZspSp1kAEOinrJjzvnTzIl44acaKsdYIymWusE/QCNO8deziprqzDZKlqIbKnkv7nH0+kNpyKHd0lyN59Q5N4+8/NfEj3vSXiPm+wAAAAASUVORK5CYII=")
	, name: "Paper Planter", color: 15844367}
planters["pesticideplanter"] := {bitmap: Gdip_BitmapFromBase64("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")
	, name: "Pesticide Planter", color: 1757484}
planters["petalplanter"] := {bitmap: Gdip_BitmapFromBase64("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")
	, name: "Petal Planter", color: 15856111}
planters["planterofplenty"] := {bitmap: Gdip_BitmapFromBase64("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")
	, name: "The Planter Of Plenty", color: 15770372}
planters["plasticplanter"] := {bitmap: Gdip_BitmapFromBase64("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")
	, name: "Plastic Planter", color: 14993703}
planters["redclayplanter"] := {bitmap: Gdip_BitmapFromBase64("iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAABv1BMVEUAAAA2NjY1NTUxMTEkJCQeHh40NDQyMjIbGxs1NTU1NTUzMzQzMzMyMjIuLi4xMTEvLy8qKioVFRU2NjY1NTU1NTU0NDQxMTEvLy80NDQyMjIvLy8qKio1NTU0NDQzMzMwMDAqKioqKiopKSk1NTUzMjEuLi4pKSmxTDXiVDI2NjawTDXqYkE3NjavSzSxTDTgVDLpYUDhVDI7NjXaUzK0TDSaOyboXz7nXD1DNzXjVjSwSzSqSTKoRy+cPSc/NjXMUjSIQzTTUjOzTDOXOyeHOCXlXj/iWDepSzVyPjVROTW6TTTOUDO/TTOuSjOZOyXQVzzlWTejSTW9TjS2TTRsPTRgOzRMODRJODRGNzTSUTOtSTKnRzKlRi+jQiyfQCnnYULhWzvkWzrmWjl/RDhwPjXGTzTCTjScRzSOQzTcUzPXUjOpSDN+QDPKTzKXQCuKOiiDOCeROSWKOCXfXkDaWTvIUzmSRjaWRjV6PzVkOzW0SzS4SjSeRC6ZQSzWWj/FVj6cSzzfWDqsTDhnPDVaOzWEQTOLQjGhRS+kQy2iQSuUPyuSPSiNOSW3Uj6xUD1ZNzJjNi50OC1wNyxxNiv+WKA8AAAAKHRSTlMA/PORDgjSxBH37r+gfm5iNCsE+ujetF07zIdvJ6iOaE8bGBXgckUdNLOUjQAAA45JREFUOMt91PV72kAYB3BCaaF0bN0qk3ZuyRuSEIK7O8W1UFZ3X13m7vIH7yLryLru+0N4Ht5PcnfvXaKQckl1S9ejVvdDW/q61B3DnQpZhge1GFCcvfoqPjU15d959mx5b99OUaAc0N3+w1TX+sFb9r94mn3y+JFeTNRgWjmcK9kpbODKiTuHeaeeCoS12ibGt55ksz5fKmAyGEwz83aqv1d0vT1Q+xJBZnwrkJopJM0MzjAMyTDmQipqizprMChM9OIAVD4/skUDs8UgzodEYXAhTBbNYR6UGh5eg9I2O1FPIsHXpB8JFiL6yTTXfYeHHdzhZG6hKAl5gj6WdaTs2GURpgnCZD55Xjv3RfQ2IlCD8yI8IAhjyoyELObk9hartxmJxYoEqQZBEKhrs4Vi0XwQLCYL6fTMdha1i7VOoNJCWYKwYyBQciwbiYw/fz4+YYtYWb6lkw6CjyEOOh7qIC5Ax6TNatULYVHfcw6jkRDhHlwV+g37C4QYI+GI5nIOh4Noj2GZ6hIaDokA8Z8YF6ZBgMN93KHxfzJVhQ4edvbAG8PZ0rjop7ALCj4XoOY0nsUMK3kvDNwV4J2rkPCd4UxpfJeCywoxF7XUfN1AGOQGHcb6rBnH/ZTyokKKjnrJJGd99cCiic9ioO5bmUknkULZo7ov/4YP+ioxhj8szIE5GAyiA8JvPCmcE3rZTt1TSfCulps7fcbIEfSfcyz0g+rWSPAa5n0jQqe7SUu3jL73NGncnQl/gxOohu+hdyN8sZXJtNw8Jd2ejQ1LC11/ctCvOoFf1zc8LtJt+RQOWSwtFxrR8ml91WOxhNZr0H3zkgQ7ACrrm6FWKBw+pt2oPPY+tBk+okfGPJnXFJxDTozqOkb5Vzczmx+XnGgN7zyWTHitQfLrGX0N2oeikvZ7mna5ecag8ujYxw8uaU3TMCg9UNrvRKztPWTo3y0iy3BO0ZbbSm4O/1deJOSwswuWT7Ucbc9oviqHaOxqHlUYhi8LF5w+dpGruxzckkFNF8TJpVU0t8YRQq5jJz4SbuKNOGBXFLLcxOxzrrUmiR+toZ1prNE42aRjfi91VSWHqi6qRL914fjSBwTpt+js5OfLHAjfHfksMe9OLEaTJE3SdCy/6y9V+U/zjc6/oUYN3kqivF+KT798lbBzFAAor/cK3Zbnihba06fsuS++A6fl+aGhQQwZNODQ+V6NbNBfPPkNPCY/BXkAAAAASUVORK5CYII=")
	, name: "Red Clay Planter", color: 15150339}
planters["tackyplanter"] := {bitmap: Gdip_BitmapFromBase64("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")
	, name: "Tacky Planter", color: 11717739}
planters["ticketplanter"] := {bitmap: Gdip_BitmapFromBase64("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")
	, name: "Ticket Planter", color: 14668392}

timers := {mobs: {bitmap: Gdip_BitmapFromBase64("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")
	, color: 9109504, values: Map()}
, machines: {bitmap: Gdip_BitmapFromBase64("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")
	, color: 16766720, values: Map()}
, beesmas: {bitmap: Gdip_BitmapFromBase64("iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAC1lBMVEUAAABkuTNitTJfqTPSdVGQakFhqjJkpDRgpjP+5l3211ZdqjBgpzNcnzJhrjNdqi5jpTVhtC5epDM9wCNenzNhojf84mBayjForTVbrzL30RmZb07z4F/+XV/y0k9fuzH91y9dnTRkrDRkojNXnyZeuTCeclKBsz1SwC6SaUOYi0FjtTJWqTD+4m3y3mTsYVhiqDT+42NWuy6gdFSrviRitzPcaVRiqDT01FKVa0RPviz72EVfsjH7WVljtzGWdU9gtTJ+ljpYlTObblBkuDJ/pDhGrC5fpTT1zyWOnD1nrzf311b63l6wf0mQYkVTqy9hsC/Uvw7+Om2LakH+4mcr0iEajBpitTJitjJhpjNfpDBjsjJnvzT/V19gpjJWqzBesy1boyv/T2L/U2D4XVxpqz1jozRjrDNhpTNgtS9YsSTP5cH/UWH+W13oZVhpuTtmrjdqxDZntDVnvDRjsDNhuDFhsTFcujBUrDBTqDBYvC9cpC9UvS3////7/v2y1Jyn1Yz/72j/aWKGxWHlaFfhalTBe02Zeky9eku4ekqbikGNcUGGpT6MnD18njtqszp8pDlqmDZrsTVvqzVkoDRcmjRfqDJVtDFdrzFaqTFPoDFXmTFatjBatDBYozBXuS5bsyhbqyZXoSZNqCVUoCPc7dG/2q2936mw2Zms0JSazYOWxHiPwG+NyWv/6WiJvmaLw2X/YmL7312rhln/UVl7tVXv11Scm1Oef1PW1VLWclLXcVLQcFF3s1DJbVD42E/TzU9tuU/Jc0+tjkuReEtyrkm1fkmvgEite0hwvEWbs0Scq0SjmUSfiUOciUP+1kKyyEL/5kGOh0GKsz6Qnj6Kkz6Kkj7/4D2AujyFlTx4lDptujm30TdrwjdqozdynjdkuTZftzVvrjVnojRgqDNXnTNXvTFYrzFTrTFMojBmwy9eoS9Uui5ivi1RtC1jsSxaridTpCZajVxKAAAAV3RSTlMA/s8a/vLizsmpmpN+em5DOy8iDwv9/Pv6+Pj29fXs7Ojn5ubg393b0MvBu7i1tLGvrq2soZ+ckpCQjYSEf395eGhnXlpZVlJQSklAPTMrKikjIh4aFggjtvGzAAACC0lEQVQ4y2JAA1zeDPiBmAU+WWtO3XVyXJw+OBV4KFzcv6nRMAS3EYFS5xv18Nlhcv3ANhkB3PJudy48n7xTPQyXfKh88dyy6nnvuXEp0L88PyI6KnV9Pz92edemvPDy6EWrazeoiGBVYHUqaXf5nKjUNanpQlgVMGucSdoVHR+1KJ0Hhxs4pp9Lyrv18qmoE3Z57rsz3vWmJJ+Y8kLSH5s8f0wEY0TN5EM5Kcd62LHIC6vGJCzNXjA3/uae2OQmW0wFAJndmJ+womFBWUZ8/P2uNhZfdHnPjS1FVVlZEdEZkZEfF1eEs6MHhdHpnJZHNeFABXGJ9fWJcUXoUcYnfiU2pbemHKQgOzsxnY0Z3Q62ZxPyktomvZkZGRcVFVXAiy7PW5BWmdaZk3t84qxZs2dPnaqIZoIgY0RZyYzpfSzJsfsuTZxXWflQB1UB09usCCCoqprQkZKc29px9mqPI7I8YDavmhvCQSBiTkXatc7W3NgtsbJBCPmA4qzldfkJIAUfqqfFV8x83Le967AWkgUxmfknM8FGNB+NioyMW7Kq7uDiB+5wBcWM4ZkJIOmE/NplSyIjIxO3rkyNuo1IFg4xhYWFMTGlpZmla/dWP5mWXlCysKhfSQjJly52ptpMEpNKohe+TptyT5nDwJzPSxgjQu0372g/0t3dzhqMK907G6tJs2pa+iGLAQCiD78p46afzQAAAABJRU5ErkJggg==")
	, color: 34622, values: Map()}}
timers.mobs.values[1] := {varname: "BugrunLadybugs", name: "Ladybugs", cooldown: 330, regex: "i)^ladybug(s)?"}
timers.mobs.values[2] := {varname: "BugrunRhinoBeetles", name: "Rhino Beetles", cooldown: 330, regex: "i)^(rhino|beetle|rhinobeetle)(s)?"}
timers.mobs.values[3] := {varname: "BugrunMantis", name: "Mantis", cooldown: 1230, regex: "i)^mantis(es)?"}
timers.mobs.values[4] := {varname: "BugrunScorpions", name: "Scorpions", cooldown: 1230, regex: "i)^scorpion(s)?"}
timers.mobs.values[5] := {varname: "BugrunSpider", name: "Spider", cooldown: 1830, regex: "i)^spider(s)?"}
timers.mobs.values[6] := {varname: "BugrunWerewolf", name: "Werewolf", cooldown: 1830, regex: "i)^(werewolf|werewolves)"}
timers.mobs.values[7] := {varname: "KingBeetle", name: "King Beetle", cooldown: 86400, regex: "i)^king(beetle)?"}
timers.mobs.values[8] := {varname: "TunnelBear", name: "Tunnel Bear", cooldown: 172800, regex: "i)^tunnel(bear)?"}
timers.mobs.values[9] := {varname: "Commando", name: "Commando", cooldown: 1800, regex: "i)^commando(chick)?"}
timers.mobs.values[10] := {varname: "CocoCrab", name: "Coco Crab", cooldown: 129600, regex: "i)^(coco(nut)?)?crab"}
timers.mobs.values[11] := {varname: "StumpSnail", name: "Stump Snail", cooldown: 345600, regex: "i)^(stump|snail|stumpsnail)"}
timers.machines.values[1] := {varname: "Clock", name: "Wealth Clock", cooldown: 3600, regex: "i)^(wealth|clock|wealthclock)"}
timers.machines.values[2] := {varname: "HoneyDis", name: "Honey Dispenser", cooldown: 3600, regex: "i)^honey(dis(penser)?)?$"}
timers.machines.values[3] := {varname: "TreatDis", name: "Treat Dispenser", cooldown: 3600, regex: "i)^treat"}
timers.machines.values[4] := {varname: "AntPass", name: "Ant Pass", cooldown: 7200, regex: "i)^ant(pass)?"}
timers.machines.values[5] := {varname: "BlueberryDis", name: "Blueberry Dispenser", cooldown: 14400, regex: "i)^blueberry"}
timers.machines.values[6] := {varname: "StrawberryDis", name: "Strawberry Dispenser", cooldown: 14400, regex: "i)^strawberry"}
timers.machines.values[7] := {varname: "Honeystorm", name: "Honeystorm", cooldown: 14400, regex: "i)^honeystorm"}
timers.machines.values[8] := {varname: "CoconutDis", name: "Coconut Dispenser", cooldown: 14400, regex: "i)^coco(nut)?(?!crab)"}
timers.machines.values[9] := {varname: "GlueDis", name: "Glue Dispenser", cooldown: 79200, regex: "i)^glue"}
timers.machines.values[10] := {varname: "RoyalJellyDis", name: "Royal Jelly Dispenser", cooldown: 79200, regex: "i)^(rj|royaljelly)"}
timers.machines.values[11] := {varname: "RoboPass", name: "Robo Pass", cooldown: 79200, regex: "i)^robo(pass)?"}
timers.machines.values[12] := {varname: "StickerPrinter", name: "Sticker Printer", cooldown: 3600, regex: "i)^sticker(printer)?"}
timers.machines.values[13] := {varname: "NormalMemoryMatch", name: "Normal Memory Match", cooldown: 7200, regex: "i)^normal(mm|memorymatch)?"}
timers.machines.values[14] := {varname: "MegaMemoryMatch", name: "Mega Memory Match", cooldown: 14400, regex: "i)^mega(mm|memorymatch)?"}
timers.machines.values[15] := {varname: "NightMemoryMatch", name: "Night Memory Match", cooldown: 28800, regex: "i)^night(mm|memorymatch)?"}
timers.machines.values[16] := {varname: "ExtremeMemoryMatch", name: "Extreme Memory Match", cooldown: 28800, regex: "i)^extreme(mm|memorymatch)?"}
timers.beesmas.values[1] := {varname: "Wreath", name: "Honey Wreath", cooldown: 1800, regex: "i)^(honey)?wreath"}
timers.beesmas.values[2] := {varname: "Stockings", name: "Stockings", cooldown: 3600, regex: "i)^stocking(s)?"}
timers.beesmas.values[3] := {varname: "Feast", name: "Beesmas Feast", cooldown: 5400, regex: "i)^(beesmas)?feast"}
timers.beesmas.values[4] := {varname: "Gingerbread", name: "Gingerbread House", cooldown: 7200, regex: "i)^ginger(bread)?(house)?"}
timers.beesmas.values[5] := {varname: "SnowMachine", name: "Snow Machine", cooldown: 7200, regex: "i)^(snowmachine|snowstorm)"}
timers.beesmas.values[6] := {varname: "Candles", name: "Candles", cooldown: 14400, regex: "i)^candle(s)?"}
timers.beesmas.values[7] := {varname: "Samovar", name: "Samovar", cooldown: 21600, regex: "i)^samovar"}
timers.beesmas.values[8] := {varname: "LidArt", name: "Lid Art", cooldown: 28800, regex: "i)^lid(art)?"}
timers.beesmas.values[9] := {varname: "GummyBeacon", name: "Gummy Beacon", cooldown: 28800, regex: "i)^(gummy)?beacon"}
timers.beesmas.values[10] := {varname: "WinterMemoryMatch", name: "Winter Memory Match", cooldown: 14400, regex: "i)^winter(mm|memorymatch)?"}

#Include "data\memorymatch.ahk"

hBitmapsSBT := Map()
#Include "%A_ScriptDir%\..\nm_image_assets\gui\blendershrine_bitmaps.ahk"

blender := Map()
blender["BlueExtract"] := {bitmap: hBitmapsSBT["BlueExtract"], name: "Blue Extract", color: 2703225}
blender["RedExtract"] := {bitmap: hBitmapsSBT["RedExtract"], name: "Red Extract", color: 12660528}
blender["Enzymes"] := {bitmap: hBitmapsSBT["Enzymes"], name: "Enzymes", color: 16777128}
blender["Oil"] := {bitmap: hBitmapsSBT["Oil"], name: "Oil", color: 16772734}
blender["Glue"] := {bitmap: hBitmapsSBT["Glue"], name: "Glue", color: 14781168}
blender["TropicalDrink"] := {bitmap: hBitmapsSBT["TropicalDrink"], name: "Tropical Drink", color: 127214241}
blender["Gumdrops"] := {bitmap: hBitmapsSBT["Gumdrops"], name: "Gumdrops", color: 75221165}
blender["MoonCharms"] := {bitmap: hBitmapsSBT["MoonCharms"], name: "Moon Charms", color: 239233142}
blender["Glitter"] := {bitmap: hBitmapsSBT["Glitter"], name: "Glitter", color: 203228226}
blender["StarJelly"] := {bitmap: hBitmapsSBT["StarJelly"], name: "Star Jelly", color: 167246235}
blender["PurplePotion"] := {bitmap: hBitmapsSBT["PurplePotion"], name: "Purple Potion", color: 150127201}
blender["SoftWax"] := {bitmap: hBitmapsSBT["SoftWax"], name: "Soft Wax", color: 252238184}
blender["HardWax"] := {bitmap: hBitmapsSBT["HardWax"], name: "Hard Wax", color: 23811572}
blender["SwirledWax"] := {bitmap: hBitmapsSBT["SwirledWax"], name: "Swirled Wax", color: 21221964}
blender["CausticWax"] := {bitmap: hBitmapsSBT["CausticWax"], name: "Caustic Wax", color: 87210100}
blender["FieldDice"] := {bitmap: hBitmapsSBT["FieldDice"], name: "Field Dice", color: 70106149}
blender["SmoothDice"] := {bitmap: hBitmapsSBT["SmoothDice"], name: "Smooth Dice", color: 164215218}
blender["LoadedDice"] := {bitmap: hBitmapsSBT["LoadedDice"], name: "Loaded Dice", color: 19910024}
blender["SuperSmoothie"] := {bitmap: hBitmapsSBT["SuperSmoothie"], name: "Super Smoothie", color: 147212145}
blender["Turpentine"] := {bitmap: hBitmapsSBT["Turpentine"], name: "Turpentine", color: 17112561}

Loop Files "patterns\*.ahk"
	patternlist .= StrReplace(A_LoopFileName, ".ahk") "|"

settings := Map(), settings.CaseSense := 0
settings["webhook"] := {enum: 1, type: "str", section: "Status", regex: "i)^(https:\/\/(canary\.|ptb\.)?(discord|discordapp)\.com\/api\/webhooks\/([\d]+)\/([a-z0-9_-]+)|<blank>)$"}
;settings["bottoken"] := {enum: 2, type: "str", section: "Status", regex: "i)^[\w-.]{50,83}$"} dangerous
;settings["MainChannelID"] := {enum: 3, type: "str", section: "Status", regex: "i)^\d{17,20}$"} dangerous
settings["ReportChannelID"] := {enum: 4, type: "str", section: "Status", regex: "i)^\d{17,20}$"}
settings["discordUID"] := {enum: 5, type: "str", section: "Status", regex: "i)^&?\d{17,20}$"}
settings["commandPrefix"] := {enum: 6, type: "str", section: "Status", regex: "i)^\S{1,3}$"}
settings["MoveMethod"] := {enum: 7, type: "str", section: "Settings", regex: "i)^(cannon|walk)$"}
settings["SprinklerType"] := {enum: 8, type: "str", section: "Settings", regex: "i)^(none|basic|silver|golden|diamond|supreme)$"}
settings["ConvertBalloon"] := {enum: 9, type: "str", section: "Settings", regex: "i)^(always|never|every|gather)$"}
settings["PrivServer"] := {enum: 10, type: "str", section: "Settings", regex: "i)^(((http(s)?):\/\/)?((www|web)\.)?roblox\.com\/games\/1537690962\/?([^\/]*)\?privateServerLinkCode=.{32}(\&[^\/]*)*|<blank>)$"}
settings["FieldName1"] := {enum: 11, type: "str", section: "Gather", regex: "i)^(Bamboo|Blue Flower|Cactus|Clover|Coconut|Dandelion|Mountain Top|Mushroom|Pepper|Pine Tree|Pineapple|Pumpkin|Rose|Spider|Strawberry|Stump|Sunflower)$"}
settings["FieldName2"] := {enum: 12, type: "str", section: "Gather", regex: "i)^(None|Bamboo|Blue Flower|Cactus|Clover|Coconut|Dandelion|Mountain Top|Mushroom|Pepper|Pine Tree|Pineapple|Pumpkin|Rose|Spider|Strawberry|Stump|Sunflower)$"}
settings["FieldName3"] := {enum: 13, type: "str", section: "Gather", regex: "i)^(None|Bamboo|Blue Flower|Cactus|Clover|Coconut|Dandelion|Mountain Top|Mushroom|Pepper|Pine Tree|Pineapple|Pumpkin|Rose|Spider|Strawberry|Stump|Sunflower)$"}
settings["FieldPattern1"] := {enum: 14, type: "str", section: "Gather", regex: "i)^(" patternlist ")$"}
settings["FieldPattern2"] := {enum: 15, type: "str", section: "Gather", regex: "i)^(" patternlist ")$"}
settings["FieldPattern3"] := {enum: 16, type: "str", section: "Gather", regex: "i)^(" patternlist ")$"}
settings["FieldPatternSize1"] := {enum: 17, type: "str", section: "Gather", regex: "i)^(XS|S|M|L|XL)$"}
settings["FieldPatternSize2"] := {enum: 18, type: "str", section: "Gather", regex: "i)^(XS|S|M|L|XL)$"}
settings["FieldPatternSize3"] := {enum: 19, type: "str", section: "Gather", regex: "i)^(XS|S|M|L|XL)$"}
settings["FieldReturnType1"] := {enum: 20, type: "str", section: "Gather", regex: "i)^(Walk|Reset)$"}
settings["FieldReturnType2"] := {enum: 21, type: "str", section: "Gather", regex: "i)^(Walk|Reset)$"}
settings["FieldReturnType3"] := {enum: 22, type: "str", section: "Gather", regex: "i)^(Walk|Reset)$"}
settings["FieldSprinklerLoc1"] := {enum: 23, type: "str", section: "Gather", regex: "i)^(Center|Upper Left|Upper|Upper Right|Right|Lower Right|Lower|Lower Left|Left)$"}
settings["FieldSprinklerLoc2"] := {enum: 24, type: "str", section: "Gather", regex: "i)^(Center|Upper Left|Upper|Upper Right|Right|Lower Right|Lower|Lower Left|Left)$"}
settings["FieldSprinklerLoc3"] := {enum: 25, type: "str", section: "Gather", regex: "i)^(Center|Upper Left|Upper|Upper Right|Right|Lower Right|Lower|Lower Left|Left)$"}
settings["FieldRotateDirection1"] := {enum: 26, type: "str", section: "Gather", regex: "i)^(None|Left|Right)$"}
settings["FieldRotateDirection2"] := {enum: 27, type: "str", section: "Gather", regex: "i)^(None|Left|Right)$"}
settings["FieldRotateDirection3"] := {enum: 28, type: "str", section: "Gather", regex: "i)^(None|Left|Right)$"}
settings["MondoAction"] := {enum: 29, type: "str", section: "Collect", regex: "i)^(Buff|Kill|Tag|Guid)$"}
settings["AntPassAction"] := {enum: 30, type: "str", section: "Collect", regex: "i)^(Pass|Challenge)$"}
settings["FieldBooster1"] := {enum: 31, type: "str", section: "Boost", regex: "i)^(None|Blue|Red|Mountain)$"}
settings["FieldBooster2"] := {enum: 32, type: "str", section: "Boost", regex: "i)^(None|Blue|Red|Mountain)$"}
settings["FieldBooster3"] := {enum: 33, type: "str", section: "Boost", regex: "i)^(None|Blue|Red|Mountain)$"}
settings["HotbarWhile2"] := {enum: 34, type: "str", section: "Boost", regex: "i)^(Never|Always|At Hive|Gathering|Attacking|Microconverter|Whirligig|Enzymes|GatherStart|Snowflake|Glitter)$"}
settings["HotbarWhile3"] := {enum: 35, type: "str", section: "Boost", regex: "i)^(Never|Always|At Hive|Gathering|Attacking|Microconverter|Whirligig|Enzymes|GatherStart|Snowflake|Glitter)$"}
settings["HotbarWhile4"] := {enum: 36, type: "str", section: "Boost", regex: "i)^(Never|Always|At Hive|Gathering|Attacking|Microconverter|Whirligig|Enzymes|GatherStart|Snowflake|Glitter)$"}
settings["HotbarWhile5"] := {enum: 37, type: "str", section: "Boost", regex: "i)^(Never|Always|At Hive|Gathering|Attacking|Microconverter|Whirligig|Enzymes|GatherStart|Snowflake|Glitter)$"}
settings["HotbarWhile6"] := {enum: 38, type: "str", section: "Boost", regex: "i)^(Never|Always|At Hive|Gathering|Attacking|Microconverter|Whirligig|Enzymes|GatherStart|Snowflake|Glitter)$"}
settings["HotbarWhile7"] := {enum: 39, type: "str", section: "Boost", regex: "i)^(Never|Always|At Hive|Gathering|Attacking|Microconverter|Whirligig|Enzymes|GatherStart|Snowflake|Glitter)$"}
settings["QuestGatherReturnBy"] := {enum: 40, type: "str", section: "Quests", regex: "i)^(Walk|Reset)$"}
settings["MoveSpeedNum"] := {enum: 41, type: "str", section: "Settings", regex: "i)^[1-9]\d(\.\d{1,3})?$"}
settings["ReconnectInterval"] := {enum: 42, type: "str", section: "Settings", regex: "i)^(1|2|3|4|6|8|12|24|<blank>)$"}
settings["ReconnectHour"] := {enum: 43, type: "str", section: "Settings", regex: "i)^(([0-1]?[0-9]|2[0-3])|<blank>)$"}
settings["ReconnectMin"] := {enum: 44, type: "str", section: "Settings", regex: "i)^(([0-5]?[0-9])?|<blank>)$"}
settings["FallbackServer1"] := {enum: 45, type: "str", section: "Settings", regex: "i)^(((http(s)?):\/\/)?((www|web)\.)?roblox\.com\/games\/1537690962\/?([^\/]*)\?privateServerLinkCode=.{32}(\&[^\/]*)*|<blank>)$"}
settings["FallbackServer2"] := {enum: 46, type: "str", section: "Settings", regex: "i)^(((http(s)?):\/\/)?((www|web)\.)?roblox\.com\/games\/1537690962\/?([^\/]*)\?privateServerLinkCode=.{32}(\&[^\/]*)*|<blank>)$"}
settings["FallbackServer3"] := {enum: 47, type: "str", section: "Settings", regex: "i)^(((http(s)?):\/\/)?((www|web)\.)?roblox\.com\/games\/1537690962\/?([^\/]*)\?privateServerLinkCode=.{32}(\&[^\/]*)*|<blank>)$"}
settings["NightAnnouncementName"] := {enum: 48, type: "str", section: "Status", regex: "i)^[^\\]$"}
settings["NightAnnouncementPingID"] := {enum: 49, type: "str", section: "Status", regex: "i)^((&?[0-9]*)|<blank>)$"}
settings["NightAnnouncementWebhook"] := {enum: 50, type: "str", section: "Status", regex: "i)^(https:\/\/(canary\.|ptb\.)?(discord|discordapp)\.com\/api\/webhooks\/([\d]+)\/([a-z0-9_-]+)|<blank>)$"}
settings["SnailTime"] := {enum: 51, type: "str", section: "Collect", regex: "i)^(5|10|15|Kill)$"}
settings["ChickTime"] := {enum: 52, type: "str", section: "Collect", regex: "i)^(5|10|15|Kill)$"}
settings["InputSnailHealth"] := {enum: 53, type: "str", section: "Collect", regex: "i)^(?:100(?:\.00?)?|\d?\d(?:\.\d\d?)?)$"}
settings["InputChickHealth"] := {enum: 54, type: "str", section: "Collect", regex: "i)^(?:100(?:\.00?)?|\d?\d(?:\.\d\d?)?)$"}
settings["ShrineItem1"] := {enum: 55, type: "str", section: "Shrine", regex: "i)^(strawberries|sunflowers|pineapples|Blueberries|blueextract|redextract|glue|oil|enzymes|gumdrops|tropicaldrink|mooncharms|glitter|starjelly|purplepotion|antpass|cloudvial|softwax|hardwax|swirledwax|causticwax|fielddice|smoothdice|loadeddice|Turpentine)$"}
settings["ShrineItem2"] := {enum: 56, type: "str", section: "Shrine", regex: "i)^(strawberries|sunflowers|pineapples|Blueberries|blueextract|redextract|glue|oil|enzymes|gumdrops|tropicaldrink|mooncharms|glitter|starjelly|purplepotion|antpass|cloudvial|softwax|hardwax|swirledwax|causticwax|fielddice|smoothdice|loadeddice|Turpentine)$"}
settings["ShrineIndex1"] := {enum: 57, type: "str", section: "Shrine", regex: "i)^(Infinite|\d{1,3})$"}
settings["ShrineIndex2"] := {enum: 58, type: "str", section: "Shrine", regex: "i)^(Infinite|\d{1,3})$"}
settings["BlenderIndex1"] := {enum: 59, type: "str", section: "Blender", regex: "i)^(Infinite|\d{1,3})$"}
settings["BlenderIndex2"] := {enum: 60, type: "str", section: "Blender", regex: "i)^(Infinite|\d{1,3})$"}
settings["BlenderIndex3"] := {enum: 61, type: "str", section: "Blender", regex: "i)^(Infinite|\d{1,3})$"}
settings["BlenderItem1"] := {enum: 62, type: "str", section: "Blender", regex: "i)^(blueextract|redextract|glue|Oil|enzymes|gumdrops|tropicaldrink|mooncharms|glitter|starjelly|purplepotion|softwax|hardwax|swirledwax|causticwax|fielddice|smoothdice|loadeddice|supersmoothie|Turpentine|None)$"}
settings["BlenderItem2"] := {enum: 63, type: "str", section: "Blender", regex: "i)^(blueextract|redextract|glue|oil|enzymes|gumdrops|tropicaldrink|mooncharms|glitter|starjelly|purplepotion|softwax|hardwax|swirledwax|causticwax|fielddice|smoothdice|loadeddice|supersmoothie|Turpentine|None)$"}
settings["BlenderItem3"] := {enum: 64, type: "str", section: "Blender", regex: "i)^(blueextract|redextract|glue|oil|enzymes|gumdrops|tropicaldrink|mooncharms|glitter|starjelly|purplepotion|softwax|hardwax|swirledwax|causticwax|fielddice|smoothdice|loadeddice|supersmoothie|Turpentine|None)$"}
settings["StickerStackItem"] := {enum: 65, type: "str", section: "Boost", regex: "i)^(tickets|sticker|sticker\+tickets)$"}
settings["StickerPrinterEgg"] := {enum: 66, type: "str", section: "Boost", regex: "i)^(Basic|Silver|Gold|Diamond|Mythic)$"}
settings["MondoLootDirection"] := {enum: 67, type: "str", section: "Collect", regex: "i)^(Left|Right|Random|Ignore)$"}
settings["PlanterName1"] := {enum: 68, type: "str", section: "Planters", regex: "i)^(PlasticPlanter|CandyPlanter|BlueClayPlanter|RedClayPlanter|TackyPlanter|PesticidePlanter|HeatTreatedPlanter|HydroponicPlanter|PetalPlanter|PlanterOfPlenty|PaperPlanter|TicketPlanter)$"}
settings["PlanterName2"] := {enum: 69, type: "str", section: "Planters", regex: "i)^(PlasticPlanter|CandyPlanter|BlueClayPlanter|RedClayPlanter|TackyPlanter|PesticidePlanter|HeatTreatedPlanter|HydroponicPlanter|PetalPlanter|PlanterOfPlenty|PaperPlanter|TicketPlanter)$"}
settings["PlanterName3"] := {enum: 70, type: "str", section: "Planters", regex: "i)^(PlasticPlanter|CandyPlanter|BlueClayPlanter|RedClayPlanter|TackyPlanter|PesticidePlanter|HeatTreatedPlanter|HydroponicPlanter|PetalPlanter|PlanterOfPlenty|PaperPlanter|TicketPlanter)$"}
settings["PlanterField1"] := {enum: 71, type: "str", section: "Planters", regex: "i)^(None|Bamboo|Blue Flower|Cactus|Clover|Coconut|Dandelion|Mountain Top|Mushroom|Pepper|Pine Tree|Pineapple|Pumpkin|Rose|Spider|Strawberry|Stump|Sunflower)$"}
settings["PlanterField2"] := {enum: 72, type: "str", section: "Planters", regex: "i)^(None|Bamboo|Blue Flower|Cactus|Clover|Coconut|Dandelion|Mountain Top|Mushroom|Pepper|Pine Tree|Pineapple|Pumpkin|Rose|Spider|Strawberry|Stump|Sunflower)$"}
settings["PlanterField3"] := {enum: 73, type: "str", section: "Planters", regex: "i)^(None|Bamboo|Blue Flower|Cactus|Clover|Coconut|Dandelion|Mountain Top|Mushroom|Pepper|Pine Tree|Pineapple|Pumpkin|Rose|Spider|Strawberry|Stump|Sunflower)$"}
settings["PlanterNectar1"] := {enum: 74, type: "str", section: "Planters", regex: "i)^(None|Comforting|Refreshing|Satisfying|Motivating|Invigorating)$"}
settings["PlanterNectar2"] := {enum: 75, type: "str", section: "Planters", regex: "i)^(None|Comforting|Refreshing|Satisfying|Motivating|Invigorating)$"}
settings["PlanterNectar3"] := {enum: 76, type: "str", section: "Planters", regex: "i)^(None|Comforting|Refreshing|Satisfying|Motivating|Invigorating)$"}
settings["PlanterHarvestFull1"] := {enum: 77, type: "str", section: "Planters", regex: "i)^(Full|Timed)$"}
settings["PlanterHarvestFull2"] := {enum: 78, type: "str", section: "Planters", regex: "i)^(Full|Timed)$"}
settings["PlanterHarvestFull3"] := {enum: 79, type: "str", section: "Planters", regex: "i)^(Full|Timed)$"}

;settings["discordMode"] := {enum: 1, type: "int", section: "Status", regex: "i)^(0|1|2)$"} dangerous
;settings["discordCheck"] := {enum: 2, type: "int", section: "Status", regex: "i)^(0|1)$"} dangerous
;settings["MainChannelCheck"] := {enum: 3, type: "int", section: "Status", regex: "i)^(0|1)$"} dangerous
settings["ReportChannelCheck"] := {enum: 4, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["WebhookEasterEgg"] := {enum: 5, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["ssCheck"] := {enum: 6, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["ssDebugging"] := {enum: 7, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["CriticalSSCheck"] := {enum: 8, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["AmuletSSCheck"] := {enum: 9, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["MachineSSCheck"] := {enum: 10, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["BalloonSSCheck"] := {enum: 11, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["ViciousSSCheck"] := {enum: 12, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["DeathSSCheck"] := {enum: 13, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["PlanterSSCheck"] := {enum: 14, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["HoneySSCheck"] := {enum: 15, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["criticalCheck"] := {enum: 16, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["CriticalErrorPingCheck"] := {enum: 17, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["DisconnectPingCheck"] := {enum: 18, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["GameFrozenPingCheck"] := {enum: 19, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["PhantomPingCheck"] := {enum: 20, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["UnexpectedDeathPingCheck"] := {enum: 21, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["EmergencyBalloonPingCheck"] := {enum: 22, type: "int", section: "Status", regex: "i)^(0|1)$"}
;settings["MacroState"] := {enum: 23, type: "int", regex: "i)^(0|1|2)$"} dangerous
settings["PlanterMode"] := {enum: 24, type: "int", section: "Planters", regex: "i)^(0|1|2)$"}
settings["MaxAllowedPlanters"] := {enum: 25, type: "int", section: "Planters", regex: "i)^(0|1|2|3)$"}
settings["HarvestInterval"] := {enum: 26, type: "int", section: "Planters", regex: "i)^\d{1,2}$"}
settings["AutomaticHarvestInterval"] := {enum: 27, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["HarvestFullGrown"] := {enum: 28, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["GotoPlanterField"] := {enum: 29, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["GatherFieldSipping"] := {enum: 30, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["ConvertFullBagHarvest"] := {enum: 31, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["GatherPlanterLoot"] := {enum: 32, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlasticPlanterCheck"] := {enum: 33, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["CandyPlanterCheck"] := {enum: 34, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["BlueClayPlanterCheck"] := {enum: 35, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["RedClayPlanterCheck"] := {enum: 36, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["TackyPlanterCheck"] := {enum: 37, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PesticidePlanterCheck"] := {enum: 38, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["HeatTreatedPlanterCheck"] := {enum: 39, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["HydroponicPlanterCheck"] := {enum: 40, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PetalPlanterCheck"] := {enum: 41, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PaperPlanterCheck"] := {enum: 42, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["TicketPlanterCheck"] := {enum: 43, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterOfPlentyCheck"] := {enum: 44, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["BambooFieldCheck"] := {enum: 45, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["BlueFlowerFieldCheck"] := {enum: 46, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["CactusFieldCheck"] := {enum: 47, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["CloverFieldCheck"] := {enum: 48, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["CoconutFieldCheck"] := {enum: 49, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["DandelionFieldCheck"] := {enum: 50, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MountainTopFieldCheck"] := {enum: 51, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MushroomFieldCheck"] := {enum: 52, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PepperFieldCheck"] := {enum: 53, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PineTreeFieldCheck"] := {enum: 54, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PineappleFieldCheck"] := {enum: 55, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PumpkinFieldCheck"] := {enum: 56, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["RoseFieldCheck"] := {enum: 57, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["SpiderFieldCheck"] := {enum: 58, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["StrawberryFieldCheck"] := {enum: 59, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["StumpFieldCheck"] := {enum: 60, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["SunflowerFieldCheck"] := {enum: 61, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MultiReset"] := {enum: 62, type: "int", section: "Settings", regex: "i)^(0|1|2|3)$"}
settings["ConvertMins"] := {enum: 63, type: "int", section: "Settings", regex: "i)^\d{1,2}$"}
settings["LastConvertBalloon"] := {enum: 64, type: "int", section: "Settings", regex: "i)^\d{1,10}$"}
settings["DisableToolUse"] := {enum: 65, type: "int", section: "Settings", regex: "i)^(0|1)$"}
settings["AnnounceGuidingStar"] := {enum: 66, type: "int", section: "Settings", regex: "i)^(0|1)$"}
settings["NewWalk"] := {enum: 67, type: "int", section: "Settings", regex: "i)^(0|1)$"}
settings["HiveSlot"] := {enum: 68, type: "int", section: "Settings", regex: "i)^[1-6]$"}
settings["HiveBees"] := {enum: 69, type: "int", section: "Settings", regex: "^([1-9]|[1-4][0-9]|50)$"}
settings["ConvertDelay"] := {enum: 70, type: "int", section: "Settings", regex: "i)^\d{1,2}$"}
settings["ReconnectMessage"] := {enum: 71, type: "int", section: "Settings", regex: "i)^(0|1)$"}
settings["PublicFallback"] := {enum: 72, type: "int", section: "Settings", regex: "i)^(0|1)$"}
settings["KeyDelay"] := {enum: 73, type: "int", section: "Settings", regex: "i)^\d{1,3}$"}
settings["FieldPatternReps1"] := {enum: 74, type: "int", section: "Gather", regex: "i)^[1-9]$"}
settings["FieldPatternReps2"] := {enum: 75, type: "int", section: "Gather", regex: "i)^[1-9]$"}
settings["FieldPatternReps3"] := {enum: 76, type: "int", section: "Gather", regex: "i)^[1-9]$"}
settings["FieldPatternShift1"] := {enum: 77, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["FieldPatternShift2"] := {enum: 78, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["FieldPatternShift3"] := {enum: 79, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["FieldPatternInvertFB1"] := {enum: 80, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["FieldPatternInvertFB2"] := {enum: 81, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["FieldPatternInvertFB3"] := {enum: 82, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["FieldPatternInvertLR1"] := {enum: 83, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["FieldPatternInvertLR2"] := {enum: 84, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["FieldPatternInvertLR3"] := {enum: 85, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["FieldUntilMins1"] := {enum: 86, type: "int", section: "Gather", regex: "i)^\d{1,4}$"}
settings["FieldUntilMins2"] := {enum: 87, type: "int", section: "Gather", regex: "i)^\d{1,4}$"}
settings["FieldUntilMins3"] := {enum: 88, type: "int", section: "Gather", regex: "i)^\d{1,4}$"}
settings["FieldUntilPack1"] := {enum: 89, type: "int", section: "Gather", regex: "i)^(5|10|15|20|25|30|35|40|45|50|55|60|65|70|75|80|85|90|95|100)$"}
settings["FieldUntilPack2"] := {enum: 90, type: "int", section: "Gather", regex: "i)^(5|10|15|20|25|30|35|40|45|50|55|60|65|70|75|80|85|90|95|100)$"}
settings["FieldUntilPack3"] := {enum: 91, type: "int", section: "Gather", regex: "i)^(5|10|15|20|25|30|35|40|45|50|55|60|65|70|75|80|85|90|95|100)$"}
settings["FieldSprinklerDist1"] := {enum: 92, type: "int", section: "Gather", regex: "i)^([1-9]|10)$"}
settings["FieldSprinklerDist2"] := {enum: 93, type: "int", section: "Gather", regex: "i)^([1-9]|10)$"}
settings["FieldSprinklerDist3"] := {enum: 94, type: "int", section: "Gather", regex: "i)^([1-9]|10)$"}
settings["FieldRotateTimes1"] := {enum: 95, type: "int", section: "Gather", regex: "i)^[1-4]$"}
settings["FieldRotateTimes2"] := {enum: 96, type: "int", section: "Gather", regex: "i)^[1-4]$"}
settings["FieldRotateTimes3"] := {enum: 97, type: "int", section: "Gather", regex: "i)^[1-4]$"}
settings["FieldDriftCheck1"] := {enum: 98, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["FieldDriftCheck2"] := {enum: 99, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["FieldDriftCheck3"] := {enum: 100, type: "int", section: "Gather", regex: "i)^(0|1)$"}
settings["ClockCheck"] := {enum: 101, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastClock"] := {enum: 102, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["MondoBuffCheck"] := {enum: 103, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastMondoBuff"] := {enum: 104, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["AntPassCheck"] := {enum: 105, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastAntPass"] := {enum: 106, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["RoboPassCheck"] := {enum: 107, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastRoboPass"] := {enum: 108, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["HoneyDisCheck"] := {enum: 109, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastHoneyDis"] := {enum: 110, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["TreatDisCheck"] := {enum: 111, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastTreatDis"] := {enum: 112, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["BlueberryDisCheck"] := {enum: 113, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastBlueberryDis"] := {enum: 114, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["StrawberryDisCheck"] := {enum: 115, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastStrawberryDis"] := {enum: 116, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["CoconutDisCheck"] := {enum: 117, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastCoconutDis"] := {enum: 118, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["RoyalJellyDisCheck"] := {enum: 119, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastRoyalJellyDis"] := {enum: 120, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["GlueDisCheck"] := {enum: 121, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastGlueDis"] := {enum: 122, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["BlueBoostCheck"] := {enum: 123, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastBlueBoost"] := {enum: 124, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["RedBoostCheck"] := {enum: 125, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastRedBoost"] := {enum: 126, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["MountainBoostCheck"] := {enum: 127, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastMountainBoost"] := {enum: 128, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["BeesmasGatherInterruptCheck"] := {enum: 129, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["StockingsCheck"] := {enum: 130, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastStockings"] := {enum: 131, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["WreathCheck"] := {enum: 132, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastWreath"] := {enum: 133, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["FeastCheck"] := {enum: 134, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastFeast"] := {enum: 135, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["GingerbreadCheck"] := {enum: 136, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastGingerbread"] := {enum: 137, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["SnowMachineCheck"] := {enum: 138, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastSnowMachine"] := {enum: 139, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["CandlesCheck"] := {enum: 140, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastCandles"] := {enum: 141, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["SamovarCheck"] := {enum: 142, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastSamovar"] := {enum: 143, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["LidArtCheck"] := {enum: 144, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastLidArt"] := {enum: 145, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["GummyBeaconCheck"] := {enum: 146, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastGummyBeacon"] := {enum: 147, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["MonsterRespawnTime"] := {enum: 148, type: "int", section: "Collect", regex: "i)^(40|[1-3]?[0-9])$"}
settings["BugrunInterruptCheck"] := {enum: 149, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["BugrunLadybugsCheck"] := {enum: 150, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["BugrunLadybugsLoot"] := {enum: 151, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastBugrunLadybugs"] := {enum: 152, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["BugrunRhinoBeetlesCheck"] := {enum: 153, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["BugrunRhinoBeetlesLoot"] := {enum: 154, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastBugrunRhinoBeetles"] := {enum: 155, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["BugrunSpiderCheck"] := {enum: 156, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["BugrunSpiderLoot"] := {enum: 157, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastBugrunSpider"] := {enum: 158, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["BugrunMantisCheck"] := {enum: 159, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["BugrunMantisLoot"] := {enum: 160, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastBugrunMantis"] := {enum: 161, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["BugrunScorpionsCheck"] := {enum: 162, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["BugrunScorpionsLoot"] := {enum: 163, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastBugrunScorpions"] := {enum: 164, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["BugrunWerewolfCheck"] := {enum: 165, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["BugrunWerewolfLoot"] := {enum: 166, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastBugrunWerewolf"] := {enum: 167, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["TunnelBearCheck"] := {enum: 168, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["TunnelBearBabyCheck"] := {enum: 169, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastTunnelBear"] := {enum: 170, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["KingBeetleCheck"] := {enum: 171, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["KingBeetleBabyCheck"] := {enum: 172, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastKingBeetle"] := {enum: 173, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["StumpSnailCheck"] := {enum: 174, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastStumpSnail"] := {enum: 175, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["CommandoCheck"] := {enum: 176, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastCommando"] := {enum: 177, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["CocoCrabCheck"] := {enum: 178, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastCocoCrab"] := {enum: 179, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["StingerCheck"] := {enum: 180, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["StingerPepperCheck"] := {enum: 181, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["StingerMountainTopCheck"] := {enum: 182, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["StingerRoseCheck"] := {enum: 183, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["StingerCactusCheck"] := {enum: 184, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["StingerSpiderCheck"] := {enum: 185, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["StingerCloverCheck"] := {enum: 186, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["BoostChaserCheck"] := {enum: 187, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["FieldBoosterMins"] := {enum: 188, type: "int", section: "Boost", regex: "i)^(0|5|10|15|20|30)$"}
settings["HotbarTime2"] := {enum: 189, type: "int", section: "Boost", regex: "i)^\d{1,5}$"}
settings["HotbarTime3"] := {enum: 190, type: "int", section: "Boost", regex: "i)^\d{1,5}$"}
settings["HotbarTime4"] := {enum: 191, type: "int", section: "Boost", regex: "i)^\d{1,5}$"}
settings["HotbarTime5"] := {enum: 192, type: "int", section: "Boost", regex: "i)^\d{1,5}$"}
settings["HotbarTime6"] := {enum: 193, type: "int", section: "Boost", regex: "i)^\d{1,5}$"}
settings["HotbarTime7"] := {enum: 194, type: "int", section: "Boost", regex: "i)^\d{1,5}$"}
settings["LastHotkey2"] := {enum: 195, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["LastHotkey3"] := {enum: 196, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["LastHotkey4"] := {enum: 197, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["LastHotkey5"] := {enum: 198, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["LastHotkey6"] := {enum: 199, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["LastHotkey7"] := {enum: 200, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["LastHotkey7"] := {enum: 201, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["LastWhirligig"] := {enum: 202, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["LastEnzymes"] := {enum: 203, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["LastGlitter"] := {enum: 204, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["LastSnowflake"] := {enum: 205, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
;settings["LastWindShrine"] := {enum: 206, type: "int", section: "Boost", regex: "i)^\d{1,10}$"} obsolete
settings["LastMicroConverter"] := {enum: 207, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["QuestGatherMins"] := {enum: 208, type: "int", section: "Quests", regex: "i)^(100|[1-9][0-9]?)$"}
settings["PolarQuestCheck"] := {enum: 209, type: "int", section: "Quests", regex: "i)^(0|1)$"}
settings["PolarQuestGatherInterruptCheck"] := {enum: 210, type: "int", section: "Quests", regex: "i)^(0|1)$"}
settings["HoneyQuestCheck"] := {enum: 211, type: "int", section: "Quests", regex: "i)^(0|1)$"}
settings["BlackQuestCheck"] := {enum: 212, type: "int", section: "Quests", regex: "i)^(0|1)$"}
settings["LastBlackQuest"] := {enum: 213, type: "int", section: "Quests", regex: "i)^\d{1,10}$"}
settings["BuckoQuestCheck"] := {enum: 214, type: "int", section: "Quests", regex: "i)^(0|1)$"}
settings["BuckoQuestGatherInterruptCheck"] := {enum: 215, type: "int", section: "Quests", regex: "i)^(0|1)$"}
settings["RileyQuestCheck"] := {enum: 216, type: "int", section: "Quests", regex: "i)^(0|1)$"}
settings["RileyQuestGatherInterruptCheck"] := {enum: 217, type: "int", section: "Quests", regex: "i)^(0|1)$"}
settings["KingBeetleAmuletMode"] := {enum: 218, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["ShellAmuletMode"] := {enum: 219, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["NightAnnouncementCheck"] := {enum: 220, type: "int", section: "Status", regex: "i)^(0|1)$"}
;settings["PublicJoined"] := {enum: 221, type: "int", regex: "i)^(0|1)$"} dangerous
settings["DebugLogEnabled"] := {enum: 222, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["StingerDailyBonusCheck"] := {enum: 223, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["GatherDoubleReset"] := {enum: 224, type: "int", section: "Settings", regex: "i)^(0|1)$"}
settings["HoneystormCheck"] := {enum: 225, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastHoneystorm"] := {enum: 226, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["RBPDelevelCheck"] := {enum: 227, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastRBPDeLevel"] := {enum: 228, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["ShrineCheck"] := {enum: 229, type: "int", section: "Shrine", regex: "i)^(0|1)$"}
settings["BlenderCheck"] := {enum: 230, type: "int", section: "Blender", regex: "i)^(0|1)$"}
settings["ShrineAmount1"] := {enum: 231, type: "int", section: "Shrine", regex: "i)^\d{1,3}$"}
settings["ShrineAmount2"] := {enum: 232, type: "int", section: "Shrine", regex: "i)^\d{1,3}$"}
settings["BlenderAmount1"] := {enum: 233, type: "int", section: "Blender", regex: "i)^\d{1,3}$"}
settings["BlenderAmount2"] := {enum: 234, type: "int", section: "Blender", regex: "i)^\d{1,3}$"}
settings["BlenderAmount3"] := {enum: 235, type: "int", section: "Blender", regex: "i)^\d{1,3}$"}
settings["BlenderRot"] := {enum: 236, type: "int", section: "Blender", regex: "i)^\d{1,3}$"}
settings["TimerInterval"] := {enum: 237, type: "int", section: "Blender", regex: "i)^\d{1,8}$"}
settings["LastBlenderRot"] := {enum: 238, type: "int", section: "Blender", regex: "i)^(1|2|3)$"}
settings["BlenderTime1"] := {enum: 239, type: "int", section: "Blender", regex: "i)^\d{1,10}$"}
settings["BlenderTime2"] := {enum: 240, type: "int", section: "Blender", regex: "i)^\d{1,10}$"}
settings["BlenderTime3"] := {enum: 241, type: "int", section: "Blender", regex: "i)^\d{1,10}$"}
settings["MondoSecs"] := {enum: 242, type: "int", section: "Collect", regex: "i)^\d{1,3}$"}
settings["MPlanterGatherA"] := {enum: 243, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterGather1"] := {enum: 244, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterGather2"] := {enum: 245, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterGather3"] := {enum: 246, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPuffModeA"] := {enum: 247, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPuffMode1"] := {enum: 248, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPuffMode2"] := {enum: 249, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPuffMode3"] := {enum: 250, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["BlueFlowerBoosterCheck"] := {enum: 251, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["BambooBoosterCheck"] := {enum: 252, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["PineTreeBoosterCheck"] := {enum: 253, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["DandelionBoosterCheck"] := {enum: 254, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["SunflowerBoosterCheck"] := {enum: 255, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["CloverBoosterCheck"] := {enum: 256, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["SpiderBoosterCheck"] := {enum: 257, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["PineappleBoosterCheck"] := {enum: 258, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["CactusBoosterCheck"] := {enum: 259, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["PumpkinBoosterCheck"] := {enum: 260, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["MushroomBoosterCheck"] := {enum: 261, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["StrawberryBoosterCheck"] := {enum: 262, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["RoseBoosterCheck"] := {enum: 263, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["MPlanterHold1"] := {enum: 264, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterHold2"] := {enum: 265, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterHold3"] := {enum: 266, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["BrownQuestCheck"] := {enum: 267, type: "int", section: "Quests", regex: "i)^(0|1)$"}
settings["LastBrownQuest"] := {enum: 268, type: "int", section: "Quests", regex: "i)^\d{1,10}$"}
settings["StickerStackCheck"] := {enum: 269, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["LastStickerStack"] := {enum: 270, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["StickerStackMode"] := {enum: 271, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["StickerStackTimer"] := {enum: 272, type: "int", section: "Boost", regex: "i)^(?!0)(?:9\d\d|\d{4}|[1-7]\d{4}|8[0-5]\d{3}|86[0-3]\d{2}|86400)$"}
settings["StickerPrinterCheck"] := {enum: 273, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["LastStickerPrinter"] := {enum: 274, type: "int", section: "Boost", regex: "i)^\d{1,10}$"}
settings["AntPassBuyCheck"] := {enum: 275, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["StickerStackHive"] := {enum: 276, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["StickerStackCub"] := {enum: 277, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["QuestBoostCheck"] := {enum: 278, type: "int", section: "Quests", regex: "i)^(0|1)$"}
settings["MConvertFullBagHarvest"] := {enum: 279, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterGatherA"] := {enum: 280, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterGather1"] := {enum: 281, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterGather2"] := {enum: 282, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterGather3"] := {enum: 283, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterSmoking1"] := {enum: 284, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterSmoking2"] := {enum: 285, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPlanterSmoking3"] := {enum: 286, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPuffModeA"] := {enum: 287, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPuffMode1"] := {enum: 288, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPuffMode2"] := {enum: 289, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["MPuffMode3"] := {enum: 290, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterHarvestNow1"] := {enum: 291, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterHarvestNow2"] := {enum: 292, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterHarvestNow3"] := {enum: 293, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterSS1"] := {enum: 294, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterSS2"] := {enum: 295, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterSS3"] := {enum: 296, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterHarvestTime1"] := {enum: 297, type: "int", section: "Planters", regex: "i)^\d{1,10}$"}
settings["PlanterHarvestTime2"] := {enum: 298, type: "int", section: "Planters", regex: "i)^\d{1,10}$"}
settings["PlanterHarvestTime3"] := {enum: 299, type: "int", section: "Planters", regex: "i)^\d{1,10}$"}
settings["PlanterEstPercent1"] := {enum: 300, type: "int", section: "Planters", regex: "i)^\d{1,3}$"}
settings["PlanterEstPercent2"] := {enum: 301, type: "int", section: "Planters", regex: "i)^\d{1,3}$"}
settings["PlanterEstPercent3"] := {enum: 302, type: "int", section: "Planters", regex: "i)^\d{1,3}$"}
settings["PlanterGlitter1"] := {enum: 303, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterGlitter2"] := {enum: 304, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterGlitter3"] := {enum: 305, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterGlitterC1"] := {enum: 306, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterGlitterC2"] := {enum: 307, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterGlitterC3"] := {enum: 308, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PlanterManualCycle1"] := {enum: 309, type: "int", section: "Planters", regex: "i)^\d$"}
settings["PlanterManualCycle2"] := {enum: 310, type: "int", section: "Planters", regex: "i)^\d$"}
settings["PlanterManualCycle3"] := {enum: 311, type: "int", section: "Planters", regex: "i)^\d$"}
settings["NormalMemoryMatchCheck"] := {enum: 312, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastNormalMemoryMatch"] := {enum: 313, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["MegaMemoryMatchCheck"] := {enum: 314, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastMegaMemoryMatch"] := {enum: 315, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["ExtremeMemoryMatchCheck"] := {enum: 316, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastExtremeMemoryMatch"] := {enum: 317, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["NightMemoryMatchCheck"] := {enum: 318, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastNightMemoryMatch"] := {enum: 319, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["WinterMemoryMatchCheck"] := {enum: 320, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["LastWinterMemoryMatch"] := {enum: 321, type: "int", section: "Collect", regex: "i)^\d{1,10}$"}
settings["MemoryMatchInterruptCheck"] := {enum: 322, type: "int", section: "Collect", regex: "i)^(0|1)$"}
settings["MicroConverterMatchIgnore"] := {enum: 323, type: "int", section: "Collect"}
settings["SunflowerSeedMatchIgnore"] := {enum: 324, type: "int", section: "Collect"}
settings["JellyBeanMatchIgnore"] := {enum: 325, type: "int", section: "Collect"}
settings["RoyalJellyMatchIgnore"] := {enum: 326, type: "int", section: "Collect"}
settings["TicketMatchIgnore"] := {enum: 327, type: "int", section: "Collect"}
settings["CyanTrimMatchIgnore"] := {enum: 328, type: "int", section: "Collect"}
settings["OilMatchIgnore"] := {enum: 329, type: "int", section: "Collect"}
settings["StrawberryMatchIgnore"] := {enum: 330, type: "int", section: "Collect"}
settings["CoconutMatchIgnore"] := {enum: 331, type: "int", section: "Collect"}
settings["TropicalDrinkMatchIgnore"] := {enum: 332, type: "int", section: "Collect"}
settings["RedExtractMatchIgnore"] := {enum: 333, type: "int", section: "Collect"}
settings["MagicBeanMatchIgnore"] := {enum: 334, type: "int", section: "Collect"}
settings["PineappleMatchIgnore"] := {enum: 335, type: "int", section: "Collect"}
settings["StarJellyMatchIgnore"] := {enum: 336, type: "int", section: "Collect"}
settings["EnzymeMatchMatchIgnore"] := {enum: 337, type: "int", section: "Collect"}
settings["BlueExtractMatchIgnore"] := {enum: 338, type: "int", section: "Collect"}
settings["GumdropMatchIgnore"] := {enum: 339, type: "int", section: "Collect"}
settings["FieldDiceMatchIgnore"] := {enum: 340, type: "int", section: "Collect"}
settings["MoonCharmMatchIgnore"] := {enum: 341, type: "int", section: "Collect"}
settings["BlueberryMatchIgnore"] := {enum: 342, type: "int", section: "Collect"}
settings["GlitterMatchIgnore"] := {enum: 343, type: "int", section: "Collect"}
settings["StingerMatchIgnore"] := {enum: 344, type: "int", section: "Collect"}
settings["TreatMatchIgnore"] := {enum: 345, type: "int", section: "Collect"}
settings["GlueMatchIgnore"] := {enum: 346, type: "int", section: "Collect"}
settings["CloudVialMatchIgnore"] := {enum: 347, type: "int", section: "Collect"}
settings["SoftWaxMatchIgnore"] := {enum: 348, type: "int", section: "Collect"}
settings["HardWaxMatchIgnore"] := {enum: 349, type: "int", section: "Collect"}
settings["SwirledWaxMatchIgnore"] := {enum: 350, type: "int", section: "Collect"}
settings["NightBellMatchIgnore"] := {enum: 351, type: "int", section: "Collect"}
settings["HoneysuckleMatchIgnore"] := {enum: 352, type: "int", section: "Collect"}
settings["SuperSmoothieMatchIgnore"] := {enum: 353, type: "int", section: "Collect"}
settings["SmoothDiceMatchIgnore"] := {enum: 354, type: "int", section: "Collect"}
settings["NeonberryMatchIgnore"] := {enum: 355, type: "int", section: "Collect"}
settings["GingerbreadMatchIgnore"] := {enum: 356, type: "int", section: "Collect"}
settings["SilverEggMatchIgnore"] := {enum: 357, type: "int", section: "Collect"}
settings["GoldEggMatchIgnore"] := {enum: 358, type: "int", section: "Collect"}
settings["DiamondEggMatchIgnore"] := {enum: 359, type: "int", section: "Collect"}
settings["CoconutBoosterCheck"] := {enum: 360, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["StumpBoosterCheck"] := {enum: 361, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["PepperBoosterCheck"] := {enum: 362, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["HoneyUpdateSSCheck"] := {enum: 363, type: "int", section: "Status", regex: "i)^(0|1)$"}
settings["StickerStackVoucher"] := {enum: 364, type: "int", section: "Boost", regex: "i)^(0|1)$"}
settings["MGatherPlanterLoot"] := {enum: 365, type: "int", section: "Planters", regex: "i)^(0|1)$"}
settings["PriorityListNumeric"] := {enum: 366, type: "int", section: "Settings", regex: "i)^[1-8]{8}$"}

bitmaps := Map()
bitmaps["moon"] := Gdip_BitmapFromBase64("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")
#Include "%A_ScriptDir%\..\nm_image_assets\offset\bitmaps.ahk"

Loop
{
	(status_buffer.Length > 0) && nm_status(status_buffer[1])
	(Mod(A_Index, 5) = 0) && discord.GetCommands(MainChannelID)
	(command_buffer.Length > 0) && nm_command(command_buffer[1])
	(Mod(A_Index, 10) = 0) && nm_honey()
	((DebugLogEnabled = 1) && (logsize > 8000000)) && nm_TrimLog(4194304) ; trim to 4MiB
	Sleep 100
}

nm_status(status)
{
	stateString := SubStr(status, InStr(status, "] ")+2)
	state := SubStr(stateString, 1, InStr(stateString, ": ")-1), objective := SubStr(stateString, InStr(stateString, ": ")+2)

	; write to debug log
	global logsize
	if (DebugLogEnabled = 1)
		try log := FileOpen("settings\debug_log.txt", "a-d"), log.WriteLine(StrReplace(status, "`n", " - ")), logsize := log.Length, log.Close()

	; send to discord
	if (discordCheck = 1)
	{
		; set colour based on state string
		static colorIndex := 0, colors := [16711680, 16744192, 16776960, 65280, 255, 4915330, 9699539]
		if (WebhookEasterEgg = 1)
			color := colors[colorIndex := Mod(colorIndex, 7) + 1]
		else
		{
			color := ((state = "Disconnected") || (state = "You Died") || (state = "Failed") || (state = "Error") || (state = "Aborting") || (state = "Missing") || (state = "Canceling") || InStr(objective, "Phantom") || InStr(objective, "No Balloon Convert")) ? 15085139 ; red - error
			: (InStr(objective, "Tunnel Bear") || InStr(objective, "King Beetle") || InStr(objective, "Vicious Bee") || InStr(objective, "Snail") || InStr(objective, "Crab") || InStr(objective, "Mondo") || InStr(objective, "Commando")) ? 7036559 ; purple - boss / attacking
			: (InStr(objective, "Planter") || (state = "Placing") || (state = "Collecting") || (state = "Holding")) ? 48355 ; blue - planters
			: ((state = "Interupted") || (state = "Reporting") || (state = "Warning")) ? 14408468 ; yellow - alert
			: ((state = "Gathering")) ? 9755247 ; light green - gathering
			: ((state = "Converting")) ? 8871681 ; yellow-brown - converting
			: ((state = "Boosted") || (state = "Looting") || (state = "Keeping") || (state = "Claimed") || (state = "Completed") || (state = "Collected") || (state = "Obtained") || InStr(stateString,"confirmed") || InStr(stateString,"found")) ? 48128 ; green - success
			: ((state = "Starting")) ? 16366336 ; orange - quests
			: ((state = "Startup") || (state = "GUI") || (state = "Detected") || (state = "Closing") || (state = "Begin") || (state = "End")) ? 15658739 ; white - startup / utility
			: 3223350
		}

		; ping
		content := ((criticalCheck = 1) && discordUID
			&& (((CriticalErrorPingCheck = 1) && (state = "Error"))
			|| ((DisconnectPingCheck = 1) && InStr(stateString, "Disconnected"))
			|| ((GameFrozenPingCheck = 1) && (InStr(stateString, "Resetting: Character") && (Mod(SubStr(objective, InStr(objective, " ")+1), 10) = 5)))
			|| ((PhantomPingCheck = 1) && InStr(stateString, "Phantom"))
			|| ((UnexpectedDeathPingCheck = 1) && (state = "You Died"))
			|| ((EmergencyBalloonPingCheck = 1) && InStr(stateString, "No Balloon Convert"))
			|| ((PlanterSSCheck = 1) && ((state = "Holding") && InStr(stateString, "Planter")))
			|| ((state = "Obtained") && InStr(stateString, "Amulet"))))
			? ("<@" discordUID ">") : ""

		; status update (embed)
		message := StrReplace(StrReplace(StrReplace(StrReplace(SubStr(status, InStr(status, "]")+1), "\", "\\"), "`n", "\n"), Chr(9), "  "), "`r")

		; screenshot
		if ((ssCheck = 1)
			&& (((CriticalSSCheck = 1) && (content != ""))
			|| ((AmuletSSCheck = 1) && InStr(stateString, "Amulet"))
			|| ((MachineSSCheck = 1) && (state = "Collected"))
			|| ((BalloonSSCheck = 1) && (stateString = "Converting: Balloon"))
			|| ((ViciousSSCheck = 1) && InStr(stateString, "Completed: Vicious Bee"))
			|| ((DeathSSCheck = 1) && (state = "You Died"))
			|| ((state = "Detected") && InStr(stateString, "Night"))
			|| ((PlanterSSCheck = 1) && (((state = "Detected") || (state = "Screenshot") || (state = "Holding")) && InStr(stateString, "Planter")))
			|| ((HoneySSCheck = 1) && InStr(stateString, "Reporting: Daily Honey LB") && ((discordMode = 0) || (channel := (StrLen(ReportChannelID) < 17) ? MainChannelID : ReportChannelID)))
			|| ((ssDebugging = 1) && ((state = "Placing") || (state = "Collecting") || (state = "Failed") || InStr(stateString, "Next Quest Step")))
			|| ((state = "Gathering") && !InStr(objective, "Ended") && (HoneyUpdateSSCheck) && (pBM := CreateHoneyBitmap(1, 0)))
			|| ((state = "Converting") && (objective = "Backpack") && (HoneyUpdateSSCheck) && (pBM := CreateHoneyBitmap()))))
		{
			if !IsSet(pBM)
				hwnd := GetRobloxHWND(), GetRobloxClientPos(hwnd), pBM := Gdip_BitmapFromScreen((windowWidth > 0) ? (windowX "|" windowY "|" windowWidth "|" windowHeight) : 0)
		}

		status_buffer.RemoveAt(1)
		discord.SendEmbed(message, color, content, pBM?, channel?), IsSet(pBM) && pBM > 0 && Gdip_DisposeImage(pBM)

		; extra: honey update
		if (ssCheck = 1)
		{
			global HoneyUpdate
			if (HoneyUpdateSSCheck && (((state = "Gathering") && !InStr(objective, "Ended")) || ((state = "Converting") && !InStr(objective, "Refreshed") && !InStr(objective, "Emptied"))))
				HoneyUpdate := (WebhookEasterEgg = 1) ? colors[colorIndex := Mod(colorIndex, 7) + 1] : color
			else if (state != "Detected")
				HoneyUpdate := 0
		}
	}
	else
		status_buffer.RemoveAt(1)

	; extra: night detection announcement
	if ((NightAnnouncementCheck = 1) && (PublicJoined = 0) && (stateString = "Detected: Night") && (StrLen(NightAnnouncementWebhook) > 0))
	{
		payload_json :=
		(
		'
		{
			' (NightAnnouncementPingID ? ('"content": "<@' NightAnnouncementPingID '>",') : '') '
			"embeds": [{
				"author": {
					"name": "Night Detected in ' (NightAnnouncementName ? (NightAnnouncementName "'s ") : "") 'Server",
					"url": "' PrivServer '",
					"icon_url": "attachment://moon.png"
				},
				"description": "A Vicious Bee **may** be found in [this server](' PrivServer ')!",
				"color": "0",
				"timestamp": "' FormatTime(A_NowUTC, "yyyy-MM-ddTHH:mm:ssZ") '"
			}]
		}
		'
		)

		discord.CreateFormData(&postdata, &contentType, [Map("name","payload_json", "content-type","application/json", "content",payload_json), Map("name","files[0]","filename","moon.png","content-type","image/png","pBitmap",bitmaps["moon"])])
		discord.SendMessageAPI(postdata, contentType, , NightAnnouncementWebhook)
	}
}

nm_honey()
{
	static id := ""
	if !HoneyUpdateSSCheck
		return id := ""
	if HoneyUpdate
	{
		payload_json := '{"embeds": [{"description": "[' A_Hour ':' A_Min ':' A_Sec '] Current Honey/Pollen", "color": "' HoneyUpdate '", "image": {"url": "attachment://honey.png"}}], "attachments": []}'
		discord.CreateFormData(&postdata, &contentType
			, [Map("name","payload_json", "content-type","application/json", "content",payload_json)
			, Map("name","files[0]", "filename","honey.png", "content-type","image/png", "pBitmap",pBM:=CreateHoneyBitmap())])
		if pBM <= 0
			return
		Gdip_DisposeImage(pBM)
		try id ? discord.EditMessageAPI(id, postdata, contentType) : ((message := JSON.parse(discord.SendMessageAPI(postdata, contentType))).Has("id") && (id := message["id"]))
	}
	else if id
		id := ""
}

CreateHoneyBitmap(honey := 1, backpack := 1)
{
	if (!honey && !backpack)
		return -1
	hwnd := GetRobloxHWND(), GetRobloxClientPos(hwnd), offsetY := GetYOffset(hwnd)
	if (windowWidth <= 500)
		return -2
	pBM := Gdip_CreateBitmap(294, (!!honey)*36 + (!!backpack)*35), G := Gdip_GraphicsFromImage(pBM)
	(honey) && (pBMHoney := Gdip_BitmapFromScreen(windowX + windowWidth//2 - 300 "|" windowY + offsetY "|294|36"), Gdip_DrawImage(G, pBMHoney), Gdip_DisposeImage(pBMHoney))
	(backpack) && (pBMBackpack := Gdip_BitmapFromScreen(windowX + windowWidth//2 "|" windowY + offsetY "|294|35"), Gdip_DrawImage(G, pBMBackpack, , (!!honey)*36), Gdip_DisposeImage(pBMBackpack))
	Gdip_DeleteGraphics(G)
	return pBM
}

nm_command(command)
{
	global commandPrefix, MacroState, planters, timers, settings, blender, shrine, priorityListNumeric
	static ssmode := "All"
	, defaultPriorityList := ["Night", "Mondo", "Planter", "Bugrun", "Collect", "QuestRotate", "Boost", "GoGather"]

	id := command.id, params := []
	Loop Parse SubStr(command.content, StrLen(commandPrefix)+1), A_Space
		if (A_LoopField != "")
			params.Push(A_LoopField)
	params.Length := 10, params.Default := ""

	switch (name := params[1]), 0
	{
		case "help","":
		switch params[2], 0
		{
			case "s","set":
			sections := Map("Boost", "**__Boost__**"
				,"Collect", "**__Collect__**"
				,"Gather", "**__Gather__**"
				,"Planters", "**__Planters__**"
				,"Quests", "**__Quests__**"
				,"Settings", "**__Settings__**"
				,"Status", "**__Status__**"
				,"Blender", "**__Blender__**"
				,"Shrine", "**__Shrine__**"), sections.Default := ""

			; populate each variable list
			for k,v in settings
				if HasProp(v, "regex")
					sections[v.section] .= "`n" k

			; trim all lists to 4096 characters (max embed description)
			for ,list in sections
				list := SubStr(list, 1, 4096)

			; split lists into max 4096 character embeds
			enum := sections.__Enum(), enum.Call(,&section)
			embeds := [], embed := Map("title", "List of Settings for ``?set``")
			Loop 10
			{
				embed["color"] := 5066239, embed["description"] := section
				Loop sections.Count
				{
					if (enum.Call(,&section) = 0)
					{
						embeds.Push(embed)
						break 2
					}
					if (StrLen(embed["description"]) + StrLen(section) > 4092) ; 4 characters for "\n\n"
						break
					else
						embed["description"] .= "`n`n" section
				}
				embeds.Push(embed.Clone()), embed.Clear()
			}

			; send embeds as separate messages (because of the max 6000 character limit)
			enum := embeds.__Enum(), enum.Call(&embed)
			postdata :=
			(
			'
			{
				"embeds": [' JSON.stringify(embed) '],
				"allowed_mentions": {
					"parse": []
				},
				"message_reference": {
					"message_id": "' id '",
					"fail_if_not_exists": false
				}
			}
			'
			)
			while enum.Call(&embed)
				discord.SendMessageAPI(postdata), postdata := '{"embeds": [' JSON.stringify(embed) ']}'

			case "a","ad","adv","advance","advanced":
			postdata :=
			(
			'
			{
				"embeds": [{
					"title": "Advanced Commands",
					"color": "7569663",
					"fields": [{
						"name": "' commandPrefix 'set [setting] [value]",
						"value": "Sets a setting to ``value`` (use ``' commandPrefix 'help set`` for a list)",
						"inline": true
					},
					{
						"name": "' commandPrefix 'get [setting]",
						"value": "Gets the current value of a setting in the macro",
						"inline": true
					},
					{
						"name": "' commandPrefix 'send [keys]",
						"value": "Uses AHK`'s ``Send`` command (see docs)",
						"inline": true
					},
					{
						"name": "' commandPrefix 'upload [filepath]",
						"value": "Uploads a specific file from ``filepath``",
						"inline": true
					},
					{
						"name": "' commandPrefix 'download [directory]",
						"value": "Downloads the attached file to ``directory``",
						"inline": true
					},
					{
						"name": "' commandPrefix 'click [options]",
						"value": "Uses AHK`'s ``Click`` command (see docs)",
						"inline": true
					},
					{
						"name": "' commandPrefix 'activate [window]",
						"value": "Uses ``WinActivate`` to activate a window",
						"inline": true
					},
					{
						"name": "' commandPrefix 'minimize [window]",
						"value": "Uses ``WinMinimize`` to minimize a window",
						"inline": true
					},
					{
						"name": "' commandPrefix 'shiftlock [on/off]",
						"value": "Enables/disables Shift Lock switch in-game",
						"inline": true
					},
					{
						"name": "' commandPrefix 'restart",
						"value": "Restarts your computer",
						"inline": true
					},
					{
						"name": "' commandPrefix 'finditem [item]",
						"value": "finds an item in your inventory and send you a screenshot",
						"inline": true
					}]
				}],
				"allowed_mentions": {
					"parse": []
				},
				"message_reference": {
					"message_id": "' id '",
					"fail_if_not_exists": false
				}
			}
			'
			)
			case "priority":
				postdata :=
				(
				'
				{
					"embeds": [
					  {
						"title": "Priority List",
						"color": 2829617,
						"description": "To change the priority list, use the following command:\n``````\n' commandPrefix 'set priorityListNumeric [numbers|default]\n``````\nEach digit represents its slot in the default priority list.\nFor example:\n``````\n' commandPrefix 'set priorityListNumeric 12345678\n``````\n\n**Default Priority List**``````ansi\n1 - Night\n2 - Mondo\n3 - Planter\n4 - Bugrun\n5 - Collect\n6 - Quest Rotate\n7 - Boost\n8 - Go Gather``````"
					  }
					],
					"allowed_mentions": {
					  "parse": []
					},
					"message_reference": {
					  "message_id": "' id '",
					  "fail_if_not_exists": false
					}
				  }			  
				'
				)
			default:
			postdata :=
			(
			'
			{
				"embeds": [{
					"title": "Useful Commands",
					"color": "5066239",
					"fields": [{
						"name": "' commandPrefix 'help",
						"value": "Display a list of useful commands",
						"inline": true
					},
					{
						"name": "' commandPrefix 'screenshot",
						"value": "Uploads a screenshot of all monitors",
						"inline": true
					},
					{
						"name": "' commandPrefix 'stop",
						"value": "Stop and reload Natro Macro (``F3``)",
						"inline": true
					},
					{
						"name": "' commandPrefix 'pause",
						"value": "Pause/unpause Natro Macro (``F2``)",
						"inline": true
					},
					{
						"name": "' commandPrefix 'start",
						"value": "Start Natro Macro (``F1``)",
						"inline": true
					},
					{
						"name": "' commandPrefix 'close [window]",
						"value": "Closes a specific window, e.g. ``' commandPrefix 'close Roblox``",
						"inline": true
					},
					{
						"name": "' commandPrefix 'rejoin (delay)",
						"value": "Closes Roblox and rejoins after an optional ``delay``",
						"inline": true
					},
					{
						"name": "' commandPrefix 'keep or ' commandPrefix 'replace",
						"value": "Keeps/replaces an amulet if prompt is on screen",
						"inline": true
					},
					{
						"name": "' commandPrefix 'log",
						"value": "Uploads your debug_log.txt",
						"inline": true
					},
					{
						"name": "' commandPrefix 'planters",
						"value": "Displays information about placed planters",
						"inline": true
					},
					{
						"name": "' commandPrefix 'timers",
						"value": "Displays information about macro timers",
						"inline": true
					},
					{
						"name": "' commandPrefix 'prefix [prefix]",
						"value": "Sets the command prefix, e.g. ``' commandPrefix 'prefix +``",
						"inline": true
					}]
				}],
				"allowed_mentions": {
					"parse": []
				},
				"message_reference": {
					"message_id": "' id '",
					"fail_if_not_exists": false
				}
			}
			'
			)
		}
		discord.SendMessageAPI(postdata)


		case "ss","screenshot":
		switch params[2], 0
		{
			case "mode":
			if ((params[3] = "all") || (params[3] = "window") || (params[3] = "screen"))
			{
				ssmode := RegExReplace(params[3], "(?:^|\.|\R)[- 0-9\*\(]*\K(.)([^\.\r\n]*)", "$U1$L2")
				discord.SendEmbed("Set screenshot mode to " ssmode "!", 5066239, , , , id)
			}
			else
				discord.SendEmbed("Invalid ``Mode``!\nMust be either ``All``, ``Window``, or ``Screen``", 16711731, , , , id)

			default:
			switch ssmode, 0
			{
				case "all":
				pBM := Gdip_BitmapFromScreen()

				case "window":
				WinGetClientPos &x, &y, &w, &h, "A"
				pBM := Gdip_BitmapFromScreen((w > 0) ? (x "|" y "|" w "|" h) : 0)

				case "screen":
				pBM := Gdip_BitmapFromScreen(1)

				default:
				discord.SendEmbed("Error: Invalid screenshot mode!", 16711731, , , , id)
				pBM := Gdip_BitmapFromScreen()
			}
			discord.SendImage(pBM, "ss.png", id)
			Gdip_DisposeImage(pBM)
		}


		case "stop","reload":
		DetectHiddenWindows 1
		if WinExist("natro_macro ahk_class AutoHotkey")
		{
			PostMessage 0x5550, 3
			discord.SendEmbed("Stopping Macro...", 5066239, , , , id)
		}
		else
			discord.SendEmbed("Error: Macro not found!", 16711731, , , , id)


		case "pause","unpause":
		if (MacroState = 0)
			discord.SendEmbed("Macro is not running!", 16711731, , , , id)
		else
		{
			DetectHiddenWindows 1
			if WinExist("natro_macro ahk_class AutoHotkey")
			{
				PostMessage 0x5550, 2
				discord.SendEmbed(((MacroState = 2) ? "Pausing" : "Unpausing") " Macro...", 5066239, , , , id)
			}
			else
				discord.SendEmbed("Error: Macro not found!", 16711731, , , , id)
		}


		case "start":
		if (MacroState = 0)
		{
			DetectHiddenWindows 1
			if WinExist("natro_macro ahk_class AutoHotkey"){
				PostMessage 0x5550, 1
				discord.SendEmbed("Starting Macro...", 5066239, , , , id)
			}
			else
				discord.SendEmbed("Error: Macro not found!", 16711731, , , , id)
		}
		else
			discord.SendEmbed("Macro has already been started!", 16711731, , , , id)


		case "close":
		DetectHiddenWindows 0
		if (hwnd := WinExist(window := Trim(SubStr(command.content, InStr(command.content, name)+StrLen(name)))))
		{
			windowPid := WinGetPID()
			DetectHiddenWindows 1
			if WinExist("natro_macro ahk_class AutoHotkey")
				natroPID := WinGetPID()
			DetectHiddenWindows 0
			if (windowPID = natroPID)
				discord.SendEmbed("Cannot close Natro Macro window!", 16711731, , , , id)
			else
			{
				title := WinGetTitle("ahk_id " hwnd)
				Loop 3
					if WinExist("ahk_id" hwnd)
						WinKill
				discord.SendEmbed('Closed Window: ``' StrReplace(StrReplace(title, "\", "\\"), '"', '\"') '``', 5066239, , , , id)
			}
		}
		else
			discord.SendEmbed('Window ``' StrReplace(StrReplace(window, "\", "\\"), '"', '\"') '`` not found!', 16711731, , , , id)


		case "activate":
		DetectHiddenWindows 0
		if (hwnd := WinExist(window := Trim(SubStr(command.content, InStr(command.content, name)+StrLen(name)))))
		{
			title := WinGetTitle("ahk_id " hwnd)
			try
			{
				WinActivate "ahk_id " hwnd
				discord.SendEmbed('Activated Window: ``' StrReplace(StrReplace(title, "\", "\\"), '"', '\"') '``', 5066239, , , , id)
			}
			catch as e
				discord.SendEmbed("Error:\n" e.Message " " e.What, 16711731, , , , id)
		}
		else
			discord.SendEmbed('Window ``' StrReplace(StrReplace(window, "\", "\\"), '"', '\"') '`` not found!', 16711731, , , , id)


		case "minimise","minimize":
		DetectHiddenWindows 0
		if (hwnd := WinExist(window := Trim(SubStr(command.content, InStr(command.content, name)+StrLen(name)))))
		{
			title := WinGetTitle("ahk_id " hwnd)
			try
			{
				WinMinimize "ahk_id " hwnd
				discord.SendEmbed('Minimized Window: ``' StrReplace(StrReplace(title, "\", "\\"), '"', '\"') '``', 5066239, , , , id)
			}
			catch as e
				discord.SendEmbed("Error:\n" e.Message " " e.What, 16711731, , , , id)
		}
		else
			discord.SendEmbed('Window ``' StrReplace(StrReplace(window, "\", "\\"), '"', '\"') '`` not found!', 16711731, , , , id)


		case "rejoin":
		if (!params[2] || ((params[2] ~= "i)^[0-9]+$") && (params[2] <= 600)))
		{
			delay := params[2] ? params[2] : 0
			DetectHiddenWindows 1
			if WinExist("natro_macro ahk_class AutoHotkey")
			{
				PostMessage 0x5557, delay
				discord.SendEmbed((delay > 0) ? ("Rejoining after " delay " seconds!") : "Rejoining...", 5066239, , , , id)
			}
			else
				discord.SendEmbed("Error: Macro not found!", 16711731, , , , id)
		}
		else
			discord.SendEmbed("Reconnect delay must be an integer less than or equal to 600!\nYou entered ``" params[2] "``.", 16711731, , , , id)


		case "log":
		discord.SendFile("settings\debug_log.txt", id)


		case "keep":
		DetectHiddenWindows 1
		if WinExist("natro_macro ahk_class AutoHotkey")
		{
			try
				result := SendMessage(0x5558, 1, , , , , , , 2000)
			catch
				result := -1
			switch result
			{
				case 2:
				discord.SendEmbed("No Roblox window found!", 16711731, , , , id)

				case 1:
				discord.SendEmbed("Kept Old Amulet", 5066239, , , , id)

				case 0:
				discord.SendEmbed("No Keep/Replace prompt found!", 16711731, , , , id)

				default:
				discord.SendEmbed("Error: SendMessage Timeout!", 16711731, , , , id)
			}
		}
		else
			discord.SendEmbed("Error: Macro not found!", 16711731, , , , id)


		case "replace":
		DetectHiddenWindows 1
		if WinExist("natro_macro ahk_class AutoHotkey")
		{
			try
				result := SendMessage(0x5558, 2, , , , , , , 2000)
			catch
				result := -1
			switch result
			{
				case 2:
				discord.SendEmbed("No Roblox window found!", 16711731, , , , id)

				case 1:
				discord.SendEmbed("Replaced Amulet!", 5066239, , , , id)

				case 0:
				discord.SendEmbed("No Keep/Replace prompt found!", 16711731, , , , id)

				default:
				discord.SendEmbed("Error: SendMessage Timeout!", 16711731, , , , id)
			}
		}
		else
			discord.SendEmbed("Error: Macro not found!", 16711731, , , , id)


		case "planter","planters":
		(vars := Map()).CaseSense := 0
		str := IniRead("settings\nm_config.ini", "Planters")
		Loop Parse str, "`n", "`r" A_Space A_Tab
			if (p := InStr(A_LoopField, "="))
				vars[SubStr(A_LoopField, 1, p-1)] := SubStr(A_LoopField, p+1)

		switch params[2], 0
		{
			case "harvest","release":
			if ((params[3] = 1) || (params[3] = 2) || (params[3] = 3))
			{
				n := params[3]
				if (vars["PlanterName" n] && (vars["PlanterName" n] != "None"))
				{
					UpdateInt("PlanterHarvestNow" n, 1, "Planters")
					UpdateInt("PlanterHarvestTime" n, nowUnix()-1, "Planters")
					discord.SendEmbed("Harvest planter in Slot " n "!", 5066239, , , , id)
				}
				else
					discord.SendEmbed("There is no planter in Slot " n "!", 16711731, , , , id)
			}
			else
				discord.SendEmbed((StrLen(params[3]) = 0) ? "You must specify a Planter Slot to harvest!" : ("Planter Slot must be 1, 2, or 3!\nYou entered " params[3] "."), 16711731, , , , id)

			case "smoking":
			if ((params[3] = 1) || (params[3] = 2) || (params[3] = 3))
			{
				vars["PlanterMode"] := IniRead("settings\nm_config.ini", "Planters", "PlanterMode")
				n := params[3]
				if (vars["PlanterName" n] && (vars["PlanterName" n] != "None") && (vars["MPlanterHold" n] = 1) && (vars["PlanterMode"] = 1))
				{
					UpdateInt("MPlanterSmoking" n, 1, "Planters")
					discord.SendEmbed("Set held planter in Slot " n " to smoking!", 5066239, , , , id)
				}
				else
					discord.SendEmbed("There is no held planter in Slot " n "!", 16711731, , , , id)
			}
			else
				discord.SendEmbed((StrLen(params[3]) = 0) ? "You must specify a Planter Slot to set as smoking!" : ("Planter Slot must be 1, 2, or 3!\nYou entered " params[3] "."), 16711731, , , , id)

			case "add":
			if ((params[4] = 1) || (params[4] = 2) || (params[4] = 3))
			{
				n := params[4]
				if (vars["PlanterName" n] && (vars["PlanterName" n] != "None"))
				{
					times := []
					Loop Parse params[3], ":"
						if (A_LoopField != "")
							times.InsertAt(1, A_LoopField)

					t := nowUnix()
					Loop 1
					{
						time_delta := 0
						Loop (m := Min(times.Length, 3))
						{
							if ((times[A_Index] ~= "i)^[0-9]+$") && (times[A_Index] < 24*(3600/(60**(A_Index-1)))))
								time_delta += times[A_Index]*(60**(A_Index-1))
							else
							{
								discord.SendEmbed("``" params[3] "`` is not a valid time!\nMake sure your time is in the form ``h:m:s`` and does not exceed 24 hours!", 16711731, , , , id)
								break 2
							}
						}

						UpdateInt("PlanterHarvestTime" n, vars["PlanterHarvestTime" n] := Max(t, vars["PlanterHarvestTime" n]) + time_delta, "Planters")
						delta := hmsFromSeconds(time_delta)
						duration := DurationFromSeconds(timer := (vars["PlanterHarvestTime" n] - nowUnix()), (timer > 0) ? (((timer >= 86400) ? "d'd' h" : "") ((timer >= 3600) ? "h'h' m" : "") ((timer >= 60) ? "m'm' s" : "") "s's'") : "'Ready'")
						discord.SendEmbed("Added " delta " to planter in Slot " n "!\nNew Remaining Time: " duration, 5066239, , , , id)
					}
				}
				else
					discord.SendEmbed("There is no planter in Slot " n "!", 16711731, , , , id)
			}
			else
				discord.SendEmbed((StrLen(params[4]) = 0) ? "You must specify a Planter Slot to add time to!" : ("Planter Slot must be 1, 2, or 3!\nYou entered " params[4] "."), 16711731, , , , id)

			case "sub","subtract":
			if ((params[4] = 1) || (params[4] = 2) || (params[4] = 3))
			{
				n := params[4]
				if (vars["PlanterName" n] && (vars["PlanterName" n] != "None"))
				{
					times := []
					Loop Parse params[3], ":"
						if (A_LoopField != "")
							times.InsertAt(1, A_LoopField)

					t := nowUnix()
					Loop 1
					{
						time_delta := 0
						Loop (m := Min(times.Length, 3))
						{
							if ((times[A_Index] ~= "i)^[0-9]+$") && (times[A_Index] < 24*(3600/(60**(A_Index-1)))))
								time_delta += times[A_Index]*(60**(A_Index-1))
							else
							{
								discord.SendEmbed("``" params[3] "`` is not a valid time!\nMake sure your time is in the form ``h:m:s`` and does not exceed 24 hours!", 16711731, , , , id)
								break 2
							}
						}

						UpdateInt("PlanterHarvestTime" n, vars["PlanterHarvestTime" n] := Max(t, vars["PlanterHarvestTime" n] - time_delta), "Planters")
						delta := hmsFromSeconds(time_delta)
						duration := DurationFromSeconds(timer := (vars["PlanterHarvestTime" n] - nowUnix()), (timer > 0) ? (((timer >= 86400) ? "d'd' h" : "") ((timer >= 3600) ? "h'h' m" : "") ((timer >= 60) ? "m'm' s" : "") "s's'") : "'Ready'")
						discord.SendEmbed("Subtracted " delta " from planter in Slot " n "!\nNew Remaining Time: " duration, 5066239, , , , id)
					}
				}
				else
					discord.SendEmbed("There is no planter in Slot " n "!", 16711731, , , , id)
			}
			else
				discord.SendEmbed((StrLen(params[4]) = 0) ? "You must specify a Planter Slot to subtract time from!" : ("Planter Slot must be 1, 2, or 3!\nYou entered " params[4] "."), 16711731, , , , id)

			case "clear":
			if ((params[3] = 1) || (params[3] = 2) || (params[3] = 3))
			{
				n := params[3]
				UpdateStr("PlanterName" n, "None", "Planters")
				UpdateStr("PlanterField" n, "None", "Planters")
				UpdateStr("PlanterNectar" n, "None", "Planters")
				UpdateStr("PlanterHarvestFull" n, "", "Planters")
				UpdateInt("PlanterHarvestTime" n, 2147483647, "Planters")
				UpdateInt("PlanterEstPercent" n, 0, "Planters")
				UpdateInt("PlanterGlitter" n, 0, "Planters")
				UpdateInt("PlanterGlitterC" n, 0, "Planters")
				discord.SendEmbed("Cleared planter in Slot " n "!", 5066239, , , , id)
			}
			else
				discord.SendEmbed((StrLen(params[3]) = 0) ? "You must specify a Planter Slot to clear!" : ("Planter Slot must be 1, 2, or 3!\nYou entered " params[3] "."), 16711731, , , , id)

			case "screenshot","ss":
			if ((params[3] = 1) || (params[3] = 2) || (params[3] = 3))
			{
				n := params[3]
				if (vars["PlanterName" n] && (vars["PlanterName" n] != "None"))
					{
						UpdateInt("PlanterSS" n, 1, "Planters")
						discord.SendEmbed("Take screenshot of planter in Slot " n "!", 5066239, , , , id)
					}
				else
					discord.SendEmbed("There is no planter in Slot " n "!", 16711731, , , , id)
			}
			else
				discord.SendEmbed((StrLen(params[3]) = 0) ? "You must specify a Planter Slot to screenshot!" : ("Planter Slot must be 1, 2, or 3!\nYou entered " params[3] "."), 16711731, , , , id)

			default:
			objParam := []
			payload_json :=
			(
			'
			{
				"allowed_mentions": {
					"parse": []
				},
				"message_reference": {
					"message_id": "' id '",
					"fail_if_not_exists": false
				},
				"embeds": [{
					"color": "5066239",
					"title": "Planters",
					"description": "The macro`'s currently placed planters are shown below.\nYou can use these commands to edit the timers:",
					"fields": [{
						"name": "' commandPrefix 'planter harvest [``n``]",
						"value": "Harvests planter in Slot ``n`` and moves to the next Slot, even if the planter is not ready or is held/smoking",
						"inline": true
					},
					{
						"name": "' commandPrefix 'planter add [``h:m:s``] [``n``]",
						"value": "Adds ``h:m:s`` to planter timer in Slot ``n``",
						"inline": true
					},
					{
						"name": "' commandPrefix 'planter sub [``h:m:s``] [``n``]",
						"value": "Subtracts ``h:m:s`` from planter timer in Slot ``n``",
						"inline": true
					},
					{
						"name": "' commandPrefix 'planter clear [``n``]",
						"value": "Clears planter in Slot ``n`` from the macro Planter Timers",
						"inline": true
					},
					{
						"name": "' commandPrefix 'planter smoking [``n``]",
						"value": "Sets held planter in Slot ``n`` to smoking (Manual planters `'disable auto harvest`' option)",
						"inline": true
					},
					{
						"name": "' commandPrefix 'planter screenshot [``n``]",
						"value": "Takes a screenshot of planter in Slot ``n``",
						"inline": true
					}]
				}
				'
			)

			t := nowUnix()
			vars["PlanterMode"] := IniRead("settings\nm_config.ini", "Planters", "PlanterMode")
			Loop 3
			{
				if (vars["PlanterName" A_Index] && (vars["PlanterName" A_Index] != "None") && planters.Has(vars["PlanterName" A_Index]))
				{
					objParam.Push(Map("name",("files[" A_Index-1 "]"),"filename",(vars["PlanterName" A_Index] ".png"),"content-type","image/png","pBitmap",planters[vars["PlanterName" A_Index]].bitmap))
					duration := DurationFromSeconds(ptimer := (vars["PlanterHarvestTime" A_Index] - t), (ptimer > 0) ? (((ptimer >= 3600) ? "h'h' m" : "") ((ptimer >= 60) ? "m'm' s" : "") "s's'") : ((vars["MPlanterSmoking" A_Index]) && (vars["PlanterMode"] = 1)) ? "'Smoking'" : ((vars["MPlanterHold" A_Index]) && (vars["PlanterMode"] = 1)) ? "'Holding'" : "'Ready'")
					payload_json .=
					(
					'
					,{
						"title": "Slot ' A_Index '",
						"author": {
							"name": "' planters[vars["PlanterName" A_Index]].name '",
							"icon_url": "attachment://' vars["PlanterName" A_Index] '.png"
						},
						"color": "' planters[vars["PlanterName" A_Index]].color '",
						"fields": [{
							"name": "Field Planted",
							"value": "' vars["PlanterField" A_Index] ' (' Format("{1:Us}", SubStr(vars["PlanterNectar" A_Index], 1, 3)) ')",
							"inline": true
						},
						{
							"name": "Time Remaining",
							"value": "' duration '",
							"inline": true
						},
						{
							"name": "Glitter Used",
							"value": "' (vars["PlanterGlitter" A_Index] ? "Yes" : "No") '",
							"inline": true
						}]
					}
					'
					)
				}
			}

			payload_json .= "]}"

			objParam.InsertAt(1, Map("name","payload_json","content-type","application/json","content",payload_json))
			discord.CreateFormData(&postdata, &contentType, objParam)
			discord.SendMessageAPI(postdata, contentType)
		}


		case "timers","timer","time":
		(vars := Map()).CaseSense := 0
		str := IniRead("settings\nm_config.ini", "Collect")
		Loop Parse str, "`n", "`r" A_Space A_Tab
			if (p := InStr(A_LoopField, "="))
				vars[SubStr(A_LoopField, 1, p-1)] := SubStr(A_LoopField, p+1)

		switch params[2], 0
		{
			case "reset":
			var := StrReplace(SubStr(command.content, InStr(command.content, "reset")+5), " ")
			for v in ["Mobs", "Machines", "Beesmas"]
			{
				for i,j in timers.%v%.values
				{
					if (var ~= j.regex)
					{
						varname := j.varname, displayname := j.name, section := v
						if ((vars[varname "Check"] = 1) || (section = "mobs"))
							cooldown := j.cooldown
						break 2
					}
				}
			}
			if IsSet(cooldown)
			{
				UpdateInt("Last" varname, nowUnix(), "Collect")
				duration := DurationFromSeconds(time := (cooldown*((section = "mobs") ? (1-(vars["MonsterRespawnTime"]?vars["MonsterRespawnTime"]:0)*0.01) : 1)), ((time >= 86400) ? "d'd' h" : "") ((time >= 3600) ? "h'h' m" : "") ((time >= 60) ? "m'm' s" : "") "s's'")
				discord.SendEmbed("Reset timer for " displayname "!\n" displayname " will now be " ((section = "mobs") ? "killed" : "collected") " in " duration ".", 5066239, , , , id)
			}
			else if IsSet(varname)
				discord.SendEmbed(displayname " is not enabled!\nUse ``?set`` to enable this timer first.", 16711731, , , , id)
			else
				discord.SendEmbed("``" ((StrLen(var) > 0) ? var : "<blank>") "`` is not recognised as a valid timer!\nUse ``?timers`` for a list of enabled timers.", 16711731, , , , id)

			case "ready":
			var := StrReplace(SubStr(command.content, InStr(command.content, "ready")+5), " ")
			for v in ["Mobs", "Machines", "Beesmas"]
			{
				for i,j in timers.%v%.values
				{
					if (var ~= j.regex)
					{
						varname := j.varname, displayname := j.name, section := v
						if ((vars[varname "Check"] = 1) || (section = "mobs"))
							cooldown := j.cooldown
						break 2
					}
				}
			}
			if IsSet(cooldown)
			{
				UpdateInt("Last" varname, 1, "Collect")
				discord.SendEmbed("Set " displayname " to be " ((section = "mobs") ? "killed" : "collected") " as soon as possible!", 5066239, , , , id)
			}
			else if IsSet(varname)
				discord.SendEmbed(displayname " is not enabled!\nUse ``?set`` to enable this timer first.", 16711731, , , , id)
			else
				discord.SendEmbed("``" ((StrLen(var) > 0) ? var : "<blank>") "`` is not recognised as a valid timer!\nUse ``?timers`` for a list of enabled timers.", 16711731, , , , id)

			case "add":
			var := params[3] ? StrReplace(SubStr(command.content, InStr(command.content, params[3])+StrLen(params[3])), " ") : ""
			for v in ["Mobs", "Machines", "Beesmas"]
			{
				for i,j in timers.%v%.values
				{
					if (var ~= j.regex)
					{
						varname := j.varname, displayname := j.name, section := v
						if ((vars[varname "Check"] = 1) || (section = "mobs"))
							cooldown := j.cooldown
						break 2
					}
				}
			}
			if IsSet(cooldown)
			{
				times := []
				Loop Parse params[3], ":"
					if (A_LoopField != "")
						times.InsertAt(1, A_LoopField)

				t := nowUnix()
				Loop 1
				{
					time_delta := 0
					Loop (m := Min(times.Length, 3))
					{
						if ((times[A_Index] ~= "i)^[0-9]+$") && (times[A_Index] <= 24*(3600/(60**(A_Index-1)))))
							time_delta += times[A_Index]*(60**(A_Index-1))
						else
						{
							discord.SendEmbed("``" params[3] "`` is not a valid time!\nMake sure your time is in the form ``h:m:s`` and does not exceed 24 hours!", 16711731, , , , id)
							break 2
						}
					}

					UpdateInt("Last" varname, vars["Last" varname] := Round(Max(t-(cooldown*((section = "mobs") ? (1-(vars["MonsterRespawnTime"]?vars["MonsterRespawnTime"]:0)*0.01) : 1)), vars["Last" varname]) + time_delta), "Collect")
					delta := hmsFromSeconds(time_delta)
					duration := DurationFromSeconds(timer := (vars["Last" varname] + cooldown*((section = "mobs") ? (1-(vars["MonsterRespawnTime"]?vars["MonsterRespawnTime"]:0)*0.01) : 1) - t), (timer > 0) ? (((timer >= 86400) ? "d'd' h" : "") ((timer >= 3600) ? "h'h' m" : "") ((timer >= 60) ? "m'm' s" : "") "s's'") : ((section = "mobs") ? "'Alive'" : "'Ready'"))
					discord.SendEmbed("Added " delta " to " displayname " timer!\nNew Remaining Time: " duration, 5066239, , , , id)
				}
			}
			else if IsSet(varname)
				discord.SendEmbed(displayname " is not enabled!\nUse ``?set`` to enable this timer first.", 16711731, , , , id)
			else
				discord.SendEmbed("``" ((StrLen(var) > 0) ? var : "<blank>") "`` is not recognised as a valid timer!\nUse ``?timers`` for a list of enabled timers.", 16711731, , , , id)

			case "sub","subtract":
			var := params[3] ? StrReplace(SubStr(command.content, InStr(command.content, params[3])+StrLen(params[3])), " ") : ""
			for v in ["Mobs", "Machines", "Beesmas"]
			{
				for i,j in timers.%v%.values
				{
					if (var ~= j.regex)
					{
						varname := j.varname, displayname := j.name, section := v
						if ((vars[varname "Check"] = 1) || (section = "mobs"))
							cooldown := j.cooldown
						break 2
					}
				}
			}
			if IsSet(cooldown)
			{
				times := []
				Loop Parse params[3], ":"
					if (A_LoopField != "")
						times.InsertAt(1, A_LoopField)

				t := nowUnix()
				Loop 1
				{
					time_delta := 0
					Loop (m := Min(times.Length, 3))
					{
						if ((times[A_Index] ~= "i)^[0-9]+$") && (times[A_Index] <= 24*(3600/(60**(A_Index-1)))))
							time_delta += times[A_Index]*(60**(A_Index-1))
						else
						{
							discord.SendEmbed("``" params[3] "`` is not a valid time!\nMake sure your time is in the form ``h:m:s`` and does not exceed 24 hours!", 16711731, , , , id)
							break 2
						}
					}

					UpdateInt("Last" varname, vars["Last" varname] := Round(Max(t-(cooldown*((section = "mobs") ? (1-(vars["MonsterRespawnTime"]?vars["MonsterRespawnTime"]:0)*0.01) : 1)), vars["Last" varname] - time_delta)), "Collect")
					delta := hmsFromSeconds(time_delta)
					duration := DurationFromSeconds(timer := (vars["Last" varname] + cooldown*((section = "mobs") ? (1-(vars["MonsterRespawnTime"]?vars["MonsterRespawnTime"]:0)*0.01) : 1) - t), (timer > 0) ? (((timer >= 86400) ? "d'd' h" : "") ((timer >= 3600) ? "h'h' m" : "") ((timer >= 60) ? "m'm' s" : "") "s's'") : ((section = "mobs") ? "'Alive'" : "'Ready'"))
					discord.SendEmbed("Subtracted " delta " from " displayname " timer!\nNew Remaining Time: " duration, 5066239, , , , id)
				}
			}
			else if IsSet(varname)
				discord.SendEmbed(displayname " is not enabled!\nUse ``?set`` to enable this timer first.", 16711731, , , , id)
			else
				discord.SendEmbed("``" ((StrLen(var) > 0) ? var : "<blank>") "`` is not recognised as a valid timer!\nUse ``?timers`` for a list of enabled timers.", 16711731, , , , id)

			default:
			objParam := []
			payload_json :=
			(
			'
			{
				"allowed_mentions": {
					"parse": []
				},
				"message_reference": {
					"message_id": "' id '",
					"fail_if_not_exists": false
				},
				"embeds": [{
					"color": "5066239",
					"title": "Timers",
					"description": "The macro`'s ongoing timers are shown below.\nYou can use these commands to edit them:",
					"fields": [{
						"name": "' commandPrefix 'timer reset [``var``]",
						"value": "Resets timer, e.g. sets Coco Crab to 1.5 days",
						"inline": true
					},
					{
						"name": "' commandPrefix 'timer add [``h:m:s``] [``var``]",
						"value": "Adds ``h:m:s`` to ``var```'s timer",
						"inline": true
					},
					{
						"name": "' commandPrefix 'timer sub [``h:m:s``] [``var``]",
						"value": "Subtracts ``h:m:s`` from ``var```'s timer",
						"inline": true
					}]
				}
				'
			)

			t := nowUnix()

			objParam.Push(Map("name",("files[0]"),"filename","Mobs.png","content-type","image/png","pBitmap",timers.mobs.bitmap))
			payload_json .=
			(
			'
			,{
				"author": {
					"name": "Mobs",
					"icon_url": "attachment://Mobs.png"
				},
				"color": "' timers.mobs.color '",
				"fields": [
			'
			)
			for i,j in timers.mobs.values
			{
				varname := j.varname
				duration := DurationFromSeconds(time := (vars["Last" varname] + j.cooldown*(1-(vars["MonsterRespawnTime"]?vars["MonsterRespawnTime"]:0)*0.01) - t), (time > 0) ? (((time >= 86400) ? "d'd' h" : "") ((time >= 3600) ? "h'h' m" : "") ((time >= 60) ? "m'm' s" : "") "s's'") : "'Alive'")
				payload_json .=
				(
				'
				{
					"name": "' j.name '",
					"value": "' duration '",
					"inline": true
				},'
				)
			}
			payload_json := RTrim(payload_json, ",") "]}"

			for k,v in ["Machines","Beesmas"]
			{
				n := 0
				for i,j in timers.%v%.values
					varname := j.varname, n += (vars[varname "Check"] = 1) ? 1 : 0

				if (n > 0)
				{
					objParam.Push(Map("name",("files[" k "]"),"filename",(v ".png"),"content-type","image/png","pBitmap",timers.%v%.bitmap))
					payload_json .=
					(
					'
					,{
						"author": {
							"name": "' v '",
							"icon_url": "attachment://' v '.png"
						},
						"color": "' timers.%v%.color '",
						"fields": [
						'
					)
					for i,j in timers.%v%.values
					{
						varname := j.varname
						if (vars[varname "Check"] = 1)
						{
							duration := DurationFromSeconds(time := (vars["Last" varname] + j.cooldown - t), (time > 0) ? (((time >= 86400) ? "d'd' h" : "") ((time >= 3600) ? "h'h' m" : "") ((time >= 60) ? "m'm' s" : "") "s's'") : "'Ready'")
							payload_json .=
							(
							'
							{
								"name": "' j.name '",
								"value": "' duration '",
								"inline": true
							},'
							)
						}
					}
					payload_json := RTrim(payload_json, ",") "]}"
				}
			}

			payload_json .= "]}"

			objParam.InsertAt(1, Map("name","payload_json","content-type","application/json","content",payload_json))
			discord.CreateFormData(&postdata, &contentType, objParam)
			discord.SendMessageAPI(postdata, contentType)
		}


		case "prefix":
		if ((newPrefix := SubStr(params[2], 1, 3)) && (StrLen(newPrefix) > 0))
		{
			commandPrefix := newPrefix
			IniWrite commandPrefix, "settings\nm_config.ini", "Status", "commandPrefix"
			discord.SendEmbed("Set ``" newPrefix "`` as your command prefix!" ((StrLen(params[2]) > 3) ? "\nThe maximum prefix length is 3." : ""), 5066239, , , , id)
		}
		else
			discord.SendEmbed("``" ((StrLen(params[2]) > 0) ? params[2] : "<blank>") "`` is not a valid prefix!" ((StrLen(params[2]) = 0) ? "\nYou cannot have an empty prefix!" : ""), 16711731, , , , id)


		case "set":
		switch params[2], 0
		{
			case "bugrun":
			switch params[3], 0
			{
				case "on",1:
				UpdateInt("BugrunLadybugsCheck", 1, "Collect")
				UpdateInt("BugrunRhinoBeetlesCheck", 1, "Collect")
				UpdateInt("BugrunSpiderCheck", 1, "Collect")
				UpdateInt("BugrunMantisCheck", 1, "Collect")
				UpdateInt("BugrunScorpionsCheck", 1, "Collect")
				UpdateInt("BugrunWerewolfCheck", 1, "Collect")
				discord.SendEmbed("Set ``BugrunLadybugsCheck``, ``BugrunRhinoBeetlesCheck``, ``BugrunSpiderCheck``, ``BugrunMantisCheck``, ``BugrunScorpionCheck``, ``BugrunWerewolfCheck``, to ``1``!", 5066239, , , , id)

				case "off",0:
				UpdateInt("BugrunLadybugsCheck", 0, "Collect")
				UpdateInt("BugrunRhinoBeetlesCheck", 0, "Collect")
				UpdateInt("BugrunSpiderCheck", 0, "Collect")
				UpdateInt("BugrunMantisCheck", 0, "Collect")
				UpdateInt("BugrunScorpionsCheck", 0, "Collect")
				UpdateInt("BugrunWerewolfCheck", 0, "Collect")
				discord.SendEmbed("Set ``BugrunLadybugsCheck``, ``BugrunRhinoBeetlesCheck``, ``BugrunSpiderCheck``, ``BugrunMantisCheck``, ``BugrunScorpionCheck``, ``BugrunWerewolfCheck``, to ``0``!", 5066239, , , , id)

				default:
				discord.SendEmbed("``" ((StrLen(params[3]) > 0) ? params[3] : "<blank>") "`` is not a valid setting!\n``?set bugrun`` must be followed by ``on``, ``off``, ``1``, or ``0``", 16711731, , , , id)
			}
			case "priority", "priorityList", "priorityListNumeric":
			value:=((params[3] = "default") ? 12345678 : params[3]),v := Settings["PriorityListNumeric"]
			if (value ~= v.regex)
			{
				for i,j in listArr:=StrSplit(value) {
					for k, v in listArr
						if (k !== i && j == v) {
							discord.SendEmbed("``" ((StrLen(value) > 0) ? value : "<blank>") "`` is not an acceptable value for ``PriorityListNumeric``!\n``" commandPrefix "help priority`` for help", 16711731, , , , id)
							return command_buffer.RemoveAt(1)
						}
					if !defaultPriorityList.Has(i)
						continue
					newList .= "\n" i " - " defaultPriorityList[j]
				}
				UpdateInt("PriorityListNumeric", value, "Settings")
				discord.SendEmbed("**New Priority List**: ``````" newList "``````\n\nnumeric: ``" (priorityListNumeric ?? IniRead(A_ScriptDir . "\..\settings\nm_config.ini", "settings", "PriorityListNumeric")) "``", 2829617, , , , id)
			}
			else
				discord.SendEmbed("``" ((StrLen(value) > 0) ? value : "<blank>") "`` is not an acceptable value for ``PriorityListNumeric``!\n``" commandPrefix "help priority`` for help", 16711731, , , , id)
			default:
			Loop 1
			{
				for k,v in settings
				{
					if ((k = params[2]) && HasProp(v, "regex"))
					{
						value := Trim(SubStr(command.content, InStr(command.content, params[2])+StrLen(params[2])))
						if (value ~= v.regex)
						{
							(v.type = "str") ? UpdateStr(k, (value = "<blank>") ? "" : value, v.section) : UpdateInt(k, value, v.section)
							discord.SendEmbed("Set ``" k "`` to ``" value "``!", 5066239, , , , id)
						}
						else
							discord.SendEmbed("``" ((StrLen(value) > 0) ? value : "<blank>") "`` is not an acceptable value for ``" k "``!", 16711731, , , , id)
						break 2
					}
				}
				discord.SendEmbed("``" ((StrLen(params[2]) > 0) ? params[2] : "<blank>") "`` is not a valid setting!\nUse ``?help set`` for a list of settings.", 16711731, , , , id)
			}
		}


		case "get":
		switch params[2], 0 {
			case "priority", "priorityList", "priorityListNumeric":
			prioritystring := '``````ansi'
			for i, j in StrSplit(priorityListNumeric ?? IniRead(A_ScriptDir "\..\settings\nm_config.ini", "settings", "PriorityListNumeric", '12345678')) {
				if !defaultPriorityList.Has(i) {
					UpdateInt("PriorityListNumeric", 12345678, "settings")
					discord.SendEmbed("1 - " defaultPriorityList[1] "\n2 - " defaultPriorityList[2] "\n3 - " defaultPriorityList[3] "\n4 - " defaultPriorityList[4] "\n5 - " defaultPriorityList[5] "\n6 - " defaultPriorityList[6], 0x2b2d31 ,,,, id)
				}
				prioritystring .= "\n" . i " - " defaultPriorityList[i]
			}
			discord.SendEmbed(prioritystring .= "\n``````\n\nnumeric: ``" (priorityListNumeric ?? IniRead(A_ScriptDir "\..\settings\nm_config.ini", "settings", "PriorityListNumeric", '12345678')) "``", 0x2b2d31, , , , id)

			default:
			k := StrReplace(Trim(SubStr(command.content, InStr(command.content, name)+StrLen(name))), " ")
			str := ""
			try ini := FileOpen("settings\nm_config.ini", "r"), str := ini.Read(), ini.Close()
			Loop Parse str, "`n", "`r" A_Space A_Tab
			{
				switch (c := SubStr(A_LoopField, 1, 1))
				{
					case "[",";":
					continue

					default:
					if ((p := InStr(A_LoopField, "=")) && (k = SubStr(A_LoopField, 1, p-1)))
					{
						k := SubStr(A_LoopField, 1, p-1), v := SubStr(A_LoopField, p+1), s := 1
						break
					}
				}
			}
			if IsSet(s)
			{
				postdata :=
				(
				'
				{
					"embeds": [{
						"color": "5066239",
						"fields": [{
								"name": "' k '",
								"value": "' ((StrLen(v) > 0) ? v : "<blank>") '"
							}
						]
					}],
					"allowed_mentions": {
						"parse": []
					},
					"message_reference": {
						"message_id": "' id '",
						"fail_if_not_exists": false
					}
				}
				'
				)
				discord.SendMessageAPI(postdata)
			}
			else
				discord.SendEmbed("``" (k ? k : "<blank>") "`` is not a valid variable!", 16711731, , , , id)
		}

		case "send":
		Send (options := Trim(SubStr(command.content, InStr(command.content, name)+StrLen(name))))
		discord.SendEmbed('Used ``````ahk\nSend \"' StrReplace(options, '"', '\"') '\"``````', 5066239, , , , id)


		case "upload":
		discord.SendFile(Trim(SubStr(command.content, InStr(command.content, name)+StrLen(name))), id)


		case "download":
		if (url := command.url)
		{
			path := StrReplace(RTrim(StrReplace(Trim(SubStr(command.content, InStr(command.content, name)+StrLen(name))), "/", "\"), "\"), "\\", "\"), message := ""
			if (StrLen(path) > 0)
			{
				if !FileExist(path)
				{
					try
						DirCreate(path), message .= 'Created folder ``' StrReplace(StrReplace(path, "\", "\\"), '"', '\"') '``\n'
					catch as e
						message .= "DirCreate Error:\n" e.Message " " e.What "\n\n"
				}
				if InStr(FileExist(path), "D")
				{
					SplitPath url, &filename
					(pos := InStr(filename, "?")) && (filename := SubStr(filename, 1, pos-1))
					try
					{
						Download url, (path .= "\" filename)
						discord.SendEmbed(message .= 'Downloaded ``' StrReplace(StrReplace(path, "\", "\\"), '"', '\"') '``', 5066239, , , , id)
					}
					catch as e
						discord.SendEmbed(message .= "Download Error:\n" e.Message " " e.What, 16711731, , , , id)
				}
			}
			else
				discord.SendEmbed("You must specify a valid directory!", 16711731, , , , id)
		}
		else
			discord.SendEmbed("No attachment found to download!", 16711731, , , , id)


		case "click":
		switch params[2], 0
		{
			case "mode":
			if ((params[3] = "screen") || (params[3] = "relative") || (params[3] = "window") || (params[3] = "client"))
			{
				CoordMode "Mouse", params[3]
				discord.SendEmbed("Used ``````ahk\nCoordMode, Mouse, " RegExReplace(params[3], "(?:^|\.|\R)[- 0-9\*\(]*\K(.)([^\.\r\n]*)", "$U1$L2") "``````", 5066239, , , , id)
			}
			else
				discord.SendEmbed("Invalid ``CoordMode``!\nMust be either ``Screen``, ``Relative``, ``Window``, or ``Client``", 16711731, , , , id)

			default:
			options := Trim(SubStr(command.content, InStr(command.content, name)+StrLen(name)))
			if (InStr(options, "WheelUp") || InStr(options, "WheelDown") || InStr(options, "WU") || InStr(options, "WD"))
			{
				for k,v in ["WheelUp","WheelDown","WU","WD"]
				{
					if (p := InStr(options, v))
					{
						Loop Parse SubStr(options, p + StrLen(v) + 1), A_Space
						{
							if (A_LoopField != "")
							{
								count := A_LoopField
								break
							}
						}

						if IsSet(count)
						{
							if (count ~= "i)^[0-9]+$")
							{
								options := SubStr(options, 1, p + StrLen(v))
								Loop count
								{
									Click options
									Sleep 50
								}
								discord.SendEmbed('Used ``````ahk\nLoop ' count '\n{\n  Click \"' options '\"\n  Sleep 50\n}``````', 5066239, , , , id)
							}
							else
								discord.SendEmbed("Click options are not valid!\nWheel scroll count must be an integer!", 16711731, , , , id)
						}
						else
						{
							Click options
							discord.SendEmbed('Used ``````ahk\nClick \"' options '\"``````', 5066239, , , , id)
						}
					}
				}
			}
			else
			{
				Click options
				discord.SendEmbed('Used ``````ahk\nClick' ((StrLen(options) > 0) ? (' \"' options '\"') : "") '``````', 5066239, , , , id)
			}
		}


		case "shiftlock":
		switch params[2], 0
		{
			case "0","1","on","off":
			state := (params[2] = "on") ? 1 : (params[2] = "off") ? 0 : params[2]
			DetectHiddenWindows 1
			if WinExist("natro_macro ahk_class AutoHotkey")
			{
				try
					result := SendMessage(0x5551, state, , , , , , , 2000)
				catch
					result := -1
				switch result
				{
					case 2:
					discord.SendEmbed("No Roblox window found!", 16711731, , , , id)

					case -1:
					discord.SendEmbed("Error: SendMessage Timeout!", 16711731, , , , id)

					default:
					discord.SendEmbed(((state = 1) ? "Enabled" : "Disabled") " Shift Lock!", 5066239, , , , id)
				}
			}
			else
				discord.SendEmbed("Error: Macro not found!", 16711731, , , , id)

			default:
			discord.SendEmbed("You must specify ``on`` or ``off``!", 16711731, , , , id)
		}


		case "restart":
		discord.SendEmbed("Restarting System...", 5066239, , , , id)
		Shutdown 6


		case "shrine":
		(vars := Map()).CaseSense := 0
		str := IniRead("settings\nm_config.ini", "Shrine")
		Loop Parse str, "`n", "`r" A_Space A_Tab
			if (p := InStr(A_LoopField, "="))
				vars[SubStr(A_LoopField, 1, p-1)] := SubStr(A_LoopField, p+1)

		switch params[2], 0
		{
			case "ready":
			if ((params[3] = 1) || (params[3] = 2) || (params[3] = 3))
			{
				n := params[3]
				Iniwrite 0, "settings\nm_config.ini", "Shrine", "LastShrine"
				IniWrite n, "settings\nm_config.ini", "Shrine", "ShrineRot"
				discord.SendEmbed("Readied Slot " n "!", 5066239, , , , id)
			}
			else
				discord.SendEmbed((StrLen(params[3]) = 0) ? "You must specify a slot to make ready!" : ("Slot must be 1, 2, or 3!\nYou entered " params[3] "."), 16711731, , , , id)

			case "clear":
			if ((params[3] = 1) || (params[3] = 2) || (params[3] = 3))
			{
				n := params[3]
				IniWrite "None", "settings\nm_config.ini", "Shrine", "ShrineItem" n
				IniWrite "0", "settings\nm_config.ini", "Shrine", "ShrineAmount" n
				Iniwrite "1", "settings\nm_config.ini", "Shrine", "ShrineIndex" n
				Iniwrite "0", "settings\nm_config.ini", "Shrine", "LastShrine"
				DetectHiddenWindows 1
				if WinExist("natro_macro ahk_class AutoHotkey") {
					PostMessage 0x5552, 230+n, 0 ; ShrineAmount
					PostMessage 0x5553, 56+n, 9 ; ShrineIndex
					PostMessage 0x5553, 54+n, 9 ; ShrineItem
				}
				discord.SendEmbed("Cleared Slot " n "!", 5066239, , , , id)
			}
			else
				discord.SendEmbed((StrLen(params[3]) = 0) ? "You must specify a slot to clear!" : ("Slot must be 1, 2, or 3!\nYou entered " params[3] "."), 16711731, , , , id)

			default:
			ShrineRotTemp := (vars["ShrineRot"] = 2) ? 1 : 2, t := nowUnix(), duration := DurationFromSeconds(time := (vars["LastShrine"] + 3600 - t), (time > 0) ? (((time >= 86400) ? "d'd' h" : "") ((time >= 3600) ? "h'h' m" : "") ((time >= 60) ? "m'm' s" : "") "s's'") : "'Ready'")
			postdata :=
			(
			'
			{
				"allowed_mentions": {
					"parse": []
				},
				"message_reference": {
					"message_id": "' id '",
					"fail_if_not_exists": false
				},
				"embeds": [{
					"title": "Wind Shrine",
					"color": "5066239",
					"fields": [{
						"name": "Current Donation",
						"value": "' vars["ShrineItem" ShrineRot] '",
						"inline": true
					},
					{
						"name": "Next Donation",
						"value": "' vars["ShrineItem" ShrineRotTemp] '",
						"inline": true
					},
					{
						"name": "Time Until Next Donation",
						"value": "' duration '",
						"inline": true
					}]
				}]
			}
			'
			)
		}
		discord.SendMessageAPI(postdata)


		case "Blender":
		(vars := Map()).CaseSense := 0
		str := IniRead("settings\nm_config.ini", "Blender")
		Loop Parse str, "`n", "`r" A_Space A_Tab
			if (p := InStr(A_LoopField, "="))
			vars[SubStr(A_LoopField, 1, p-1)] := SubStr(A_LoopField, p+1)

		switch params[2], 0
		{
			case "ready":
			if ((params[3] = 1) || (params[3] = 2) || (params[3] = 3))
			{
				n := params[3]
				IniWrite 0, "settings\nm_config.ini", "Blender", "BlenderCount" n
				Iniwrite 0, "settings\nm_config.ini", "Blender", "BlenderTime" n
				IniWrite n, "settings\nm_config.ini", "Blender", "BlenderRot"
				IniWrite 1, "settings\nm_config.ini", "Blender", "BlenderEnd"
				discord.SendEmbed("Readied Slot " n "!", 5066239, , , , id)
			}
			else
				discord.SendEmbed((StrLen(params[3]) = 0) ? "You must specify a slot to make ready!" : ("Slot must be 1, 2, or 3!\nYou entered " params[3] "."), 16711731, , , , id)

			case "clear":
			if ((params[3] = 1) || (params[3] = 2) || (params[3] = 3))
			{
				n := params[3]
				IniWrite "None", "settings\nm_config.ini", "Blender", "BlenderItem" n
				IniWrite 0, "settings\nm_config.ini", "Blender", "BlenderAmount" n
				IniWrite 0, "settings\nm_config.ini", "Blender", "BlenderCount" n
				Iniwrite 1, "settings\nm_config.ini", "Blender", "BlenderIndex" n
				Iniwrite 0, "settings\nm_config.ini", "Blender", "BlenderTime" n
				IniWrite n, "settings\nm_config.ini", "Blender", "BlenderRot"
				DetectHiddenWindows 1
				if WinExist("natro_macro ahk_class AutoHotkey") {
					PostMessage 0x5552, 232+n, 0 ; BlenderAmount
					PostMessage 0x5552, 238+n, 0 ; BlenderTime
					PostMessage 0x5553, 58+n, 8 ; BlenderIndex
					PostMessage 0x5553, 61+n, 8 ; BlenderItem
				}
				discord.SendEmbed("Cleared Slot " n "!", 5066239, , , , id)

			}
			else
				discord.SendEmbed((StrLen(params[3]) = 0) ? "You must specify a slot to clear!" : ("Slot must be 1, 2, or 3!\nYou entered " params[3] "."), 16711731, , , , id)

			default:
			objParam := []
			payload_json :=
			(
			'
			{
			"allowed_mentions": {
				"parse": []
			},
			"message_reference": {
				"message_id": "' id '",
				"fail_if_not_exists": false
			},
			"embeds": [{
				"color": "5066239",
				"title": "Blender",
				"description": "The macro`'s currently rotating between the items shown below.",
				"fields": []
			}
			'
			)

			Loop 3
			{
				if ((vars["BlenderIndex" A_Index] > 0) && (vars["BlenderItem" A_Index] != "None") && blender.Has(vars["BlenderItem" A_Index]))
				{
					duration := DurationFromSeconds(btimer := (vars["BlenderTime" A_Index] - nowUnix()), (btimer > 0) ? (((btimer >= 3600) ? "h'h' m" : "") ((btimer >= 60) ? "m'm' s" : "") "s's'") : "'Ready'")

					objParam.Push(Map("name",("files[" A_Index-1 "]"),"filename",(vars["BlenderItem" A_Index] ".png"),"content-type","image/png","pBitmap",Blender[vars["BlenderItem" A_Index]].bitmap))
					payload_json .=
					(
					'
					,{
						"title": "Slot ' A_Index '",
						"author": {
							"name": "' blender[vars["BlenderItem" A_Index]].name '",
							"icon_url": "attachment://' vars["BlenderItem" A_Index] '.png"
						},
						"color": "' blender[vars["BlenderItem" A_Index]].color '",
						"fields": [{
							"name": "Item Amount",
							"value": "' vars["BlenderAmount" A_Index] '",
							"inline": true
						},
						{
							"name": "Times to loop",
								"value": "' vars["BlenderIndex" A_Index] '",
							"inline": true
						},
						{
							"name": "Time Left",
							"value": "' duration '",
							"inline": true
						}]
					}
					'
					)
				}
			}

			payload_json .= "]}"

			objParam.InsertAt(1, Map("name","payload_json","content-type","application/json","content",payload_json))
			discord.CreateFormData(&postdata, &contentType, objParam)
			discord.SendMessageAPI(postdata, contentType)
		}


		case "mm","memorymatch":
		(vars := Map()).CaseSense := 0
		str := IniRead("settings\nm_config.ini", "Collect")
		Loop Parse str, "`n", "`r" A_Space A_Tab
			if (p := InStr(A_LoopField, "="))
				vars[SubStr(A_LoopField, 1, p-1)] := SubStr(A_LoopField, p+1)

		switch params[2], 0
		{
			case "enable","disable":
			if vars.Has(var := params[3] "MemoryMatchCheck")
			{
				if (vars[var] = (params[2] = "enable"))
					discord.SendEmbed(StrTitle(params[3]) " Memory Match is already " StrLower(params[2]) "d!", 16711731, , , , id)
				else
					UpdateInt(var, (params[2] = "enable"), "Collect"), discord.SendEmbed(StrTitle(params[2]) "d " StrTitle(params[3]) " Memory Match!", 5066239, , , , id)
			}
			else
				discord.SendEmbed((StrLen(params[3]) = 0) ? ("You must specify a game to " StrLower(params[2]) "!") : ("[``game``] must be one of the following: ``normal``, ``mega``, ``night``, ``extreme``, ``winter``!\nYou entered " params[3] "."), 16711731, , , , id)

			case "ignore":
			Loop 1
			{
				for item, data in MemoryMatch
				{
					if (item = params[3])
					{
						var := item "MatchIgnore"
						switch game := StrTitle(params[4])
						{
							case "Normal","Mega","Night","Extreme","Winter":
							if (data.games & (bit := MemoryMatchGames[game].bit))
							{
								if (vars[var] & bit)
									UpdateInt(var, vars[var] & ~bit, "Collect"), discord.SendEmbed("Set " data.name " to be **matched** in " game " Memory Match!", 5066239, , , , id)
								else
									UpdateInt(var, vars[var] | bit, "Collect"), discord.SendEmbed("Set " data.name " to be **ignored** in " game " Memory Match!", 5066239, , , , id)
							}
							else
								discord.SendEmbed(data.name " are not a possible item in " game " Memory Match!", 16711731, , , , id)

							case "":
							if (vars[var] > 0)
								UpdateInt(var, 0, "Collect"), discord.SendEmbed("Set " data.name " to be **matched** in all Memory Match games!", 5066239, , , , id)
							else
								UpdateInt(var, data.games, "Collect"), discord.SendEmbed("Set " data.name " to be **ignored** in all Memory Match games!", 5066239, , , , id)

							default:
							discord.SendEmbed("You entered an invalid game!\n[``game``] must be either left blank, or one of the following: ``normal``, ``mega``, ``night``, ``extreme``, ``winter``!\nYou entered " params[4] ".", 16711731, , , , id)
						}
						break 2
					}
				}
				discord.SendEmbed((StrLen(params[3]) = 0) ? ("You must specify an item to ignore!") : ("[``item``] must be a valid item name, e.g. ``royaljelly``!\nYou entered " params[3] "."), 16711731, , , , id)
			}

			default:
			postdata :=
			(
			'
			{
				"allowed_mentions": {
					"parse": []
				},
				"message_reference": {
					"message_id": "' id '",
					"fail_if_not_exists": false
				},
				"embeds": [{
					"color": "5066239",
					"title": "Memory Match",
					"description": "The macro`'s currently enabled Memory Match games are shown below.\nYou can use ``' commandPrefix 'timers`` to change the timers, and you can also use these commands to change certain Memory Match settings:",
					"fields": [{
						"name": "' commandPrefix 'mm enable [``game``]",
						"value": "Enables the macro playing the selected Memory Match game",
						"inline": true
					},
					{
						"name": "' commandPrefix 'mm disable [``game``]",
						"value": "Disables the macro playing the selected Memory Match game",
						"inline": true
					},
					{
						"name": "' commandPrefix 'mm ignore [``item``] (``game``)",
						"value": "Toggles whether ``item`` is ignored (if ``game`` is omitted, ignore is turned on/off for all games)",
						"inline": true
					}]
				}
			'
			)

			for game in ["Normal", "Mega", "Night", "Extreme", "Winter"]
			{
				if (vars[game "MemoryMatchCheck"] = 1)
				{
					bit := MemoryMatchGames[game].bit, ignore := "None"
					for item, data in MemoryMatch
						if (vars[item "MatchIgnore"] & bit)
							ignore .= ", " data.name

					postdata .=
					(
					'
					,{
						"title": "' game ' Memory Match",
						"color": "5066239",
						"fields": [{
							"name": "Time Left",
							"value": "' DurationFromSeconds(time := (vars["Last" game "MemoryMatch"] + MemoryMatchGames[game].cooldown - nowUnix()), (time > 0) ? (((time >= 86400) ? "d'd' h" : "") ((time >= 3600) ? "h'h' m" : "") ((time >= 60) ? "m'm' s" : "") "s's'") : "'Ready'") '"
						},
						{
							"name": "Ignored Items",
							"value": "' StrReplace(ignore, "None, ") '"
						}]
					}
					'
					)
				}
			}

			postdata .= "]}"
			discord.SendMessageAPI(postdata)
		}
		
		case "FindItem":
		static items := ["Cog", "Ticket", "SprinklerBuilder", "BeequipCase", "Gumdrops", "Coconut", "Stinger", "Snowflake", "MicroConverter", "Honeysuckle", "Whirligig", "FieldDice", "SmoothDice", "LoadedDice", "JellyBeans", "RedExtract", "BlueExtract", "Glitter", "Glue", "Oil", "Enzymes", "TropicalDrink", "PurplePotion", "SuperSmoothie", "MarshmallowBee", "Sprout", "MagicBean", "FestiveBean", "CloudVial", "NightBell", "BoxOFrogs", "AntPass", "BrokenDrive", "7ProngedCog", "RoboPass", "Translator", "SpiritPetal", "Present", "Treat", "StarTreat", "AtomicTreat", "SunflowerSeed", "Strawberry", "Pineapple", "Blueberry", "Bitterberry", "Neonberry", "MoonCharm", "GingerbreadBear", "AgedGingerbreadBear", "WhiteDrive", "RedDrive", "BlueDrive", "GlitchedDrive", "ComfortingVial", "InvigoratingVial", "MotivatingVial", "RefreshingVial", "SatisfyingVial", "PinkBalloon", "RedBalloon", "WhiteBalloon", "BlackBalloon", "SoftWax", "HardWax", "CausticWax", "SwirledWax", "Turpentine", "PaperPlanter", "TicketPlanter", "FestivePlanter", "PlasticPlanter", "CandyPlanter", "RedClayPlanter", "BlueClayPlanter", "TackyPlanter", "PesticidePlanter", "HeatTreatedPlanter", "HydroponicPlanter", "PetalPlanter", "ThePlanterOfPlenty", "BasicEgg", "SilverEgg", "GoldEgg", "DiamondEgg", "MythicEgg", "StarEgg", "GiftedSilverEgg", "GiftedGoldEgg", "GiftedDiamondEgg", "GiftedMythicEgg", "RoyalJelly", "StarJelly", "BumbleBeeEgg", "BumbleBeeJelly", "RageBeeJelly", "ShockedBeeJelly"]
		UI := SubStr(command.content, StrLen(commandPrefix)+10) ; user input
		if !(UI) {
			command_buffer.RemoveAt(1)
			return discord.SendEmbed("Missing item name!\n``````?finditem [itemname]``````", 16711731, , , , id)
		}
		closestItem:=findClosestItem(items,UI)
		if closestItem.dist > 6 || not closestItem.item
			discord.SendEmbed("Item ``" UI "`` is not valid", 5066239, , , , id)
		else
			DetectHiddenWindows 1
			if WinExist("natro_macro ahk_class AutoHotkey")
				SendMessage(0x5559, ObjHasValue(items,closestItem.item),,,,,,,2000)	
			DetectHiddenWindows 0


		#Include "*i %A_ScriptDir%\..\settings\personal_commands.ahk"


		default:
		discord.SendEmbed("``" commandPrefix name "`` is not a valid command!\nUse ``" commandPrefix "help`` for a list of commonly used commands.", 16711731, , , , id)
	}

	command_buffer.RemoveAt(1)
	LevenshteinDistance(s1, s2) {
		len1 := StrLen(s1), len2 := StrLen(s2)
		s1 := StrSplit(s1), s2 := StrSplit(s2)
		d := {}, d.0 := { 0: 0 }
		Loop len1
			d.%A_Index% := { 0: A_Index }
		Loop len2
			d.0.%A_Index% := A_Index
		Loop len1 {
			i := A_Index
			Loop len2 {
				j := A_Index  ; only for simplicity
				cost := s1[i] != s2[j]
				d.%i%.%j% := Min(d.%i - 1%.%j% + 1, d.%i%.%j - 1% + 1, d.%i - 1%.%j - 1% + cost)
			}
		}
		return d.%len1%.%len2%
	}
	findClosestItem(arr,needle) {
		dist := StrLen(needle)
		for i,v in arr
			if (d := LevenshteinDistance(needle, v)) < dist
				dist := d, item := v
		if !IsSet(item)
			return {item:0,dist:100} ;large dist to break
		return {item:item,dist:dist}
	}
	ObjHasValue(obj, value) {
		for k,v in obj
			if (v = value)
			return k
		return 0
	}
}

class discord
{
	static baseURL := "https://discord.com/api/v10/"

	static SendEmbed(message, color:=3223350, content:="", pBitmap:=0, channel:="", replyID:=0)
	{
		payload_json :=
		(
		'
		{
			"content": "' content '",
			"embeds": [{
				"description": "' message '",
				"color": "' color '"
				' (pBitmap ? (',"image": {"url": "attachment://ss.png"}') : '') '
			}]
			' (replyID ? (',"allowed_mentions": {"parse": []}, "message_reference": {"message_id": "' replyID '", "fail_if_not_exists": false}') : '') '
		}
		'
		)

		if pBitmap
			this.CreateFormData(&postdata, &contentType, [Map("name","payload_json","content-type","application/json","content",payload_json), Map("name","files[0]","filename","ss.png","content-type","image/png","pBitmap",pBitmap)])
		else
			postdata := payload_json, contentType := "application/json"

		return this.SendMessageAPI(postdata, contentType, channel)
	}

	static SendFile(filepath, replyID:=0)
	{
		static MimeTypes := Map("PNG", "image/png"
			, "JPEG", "image/jpeg"
			, "JPG", "image/jpeg"
			, "BMP", "image/bmp"
			, "GIF", "image/gif"
			, "WEBP", "image/webp"
			, "TXT", "text/plain"
			, "INI", "text/plain")

		if (attr := FileExist(filepath))
		{
			SplitPath filepath := RTrim(filepath, "\/"), &file:=""
			if (file && InStr(attr, "D"))
			{
				; attempt to zip folder to temp
				try
				{
					RunWait 'powershell.exe -WindowStyle Hidden -Command Compress-Archive -Path "' filepath '\*" -DestinationPath "$env:TEMP\' file '.zip" -CompressionLevel Fastest -Force', , "Hide"
					if !FileExist(filepath := A_Temp "\" file ".zip")
						throw
				}
				catch
				{
					this.SendEmbed('The folder ``' StrReplace(StrReplace(filepath, "\", "\\"), '"', '\"') '`` could not be zipped!`nThis function is only supported on Windows 10 or higher.', 16711731, , , , replyID)
					return -3
				}
			}
			size := FileGetSize(filepath)
			if (size > 26214076)
			{
				this.SendEmbed('``' StrReplace(StrReplace(filepath, "\", "\\"), '"', '\"') '`` is above the Discord file size limit of 25MiB!', 16711731, , , , replyID)
				return -1
			}
		}
		else
		{
			this.SendEmbed('``' StrReplace(StrReplace(filepath, "\", "\\"), '"', '\"') '`` does not exist or could not be read!', 16711731, , , , replyID)
			return -2
		}

		SplitPath filepath, &file, , &ext
		ext := StrUpper(ext)
		params := []
		(replyID > 0) && params.Push(Map("name","payload_json","content-type","application/json","content",'{"allowed_mentions": {"parse": []}, "message_reference": {"message_id": "' replyID '", "fail_if_not_exists": false}}'))
		params.Push(Map("name","files[0]","filename",file,"content-type",MimeTypes.Has(ext) ? MimeTypes[ext] : "application/octet-stream","file",filepath))
		this.CreateFormData(&postdata, &contentType, params)
		this.SendMessageAPI(postdata, contentType)

		; delete any temp file created
		if (SubStr(filepath, 1, StrLen(A_Temp)) = A_Temp)
			try FileDelete filepath
	}

	static SendImage(pBitmap, imgname:="image.png", replyID:=0)
	{
		params := []
		(replyID > 0) && params.Push(Map("name","payload_json","content-type","application/json","content",'{"allowed_mentions": {"parse": []}, "message_reference": {"message_id": "' replyID '", "fail_if_not_exists": false}}'))
		params.Push(Map("name","files[0]","filename",imgname,"content-type","image/png","pBitmap",pBitmap))
		this.CreateFormData(&postdata, &contentType, params)
		this.SendMessageAPI(postdata, contentType)
	}

	static SendMessageAPI(postdata, contentType:="application/json", channel:="", url:="")
	{
		global webhook, bottoken, discordMode, MainChannelCheck, MainChannelID

		if (!channel && (discordMode = 1))
		{
			if (MainChannelCheck = 1)
				channel := MainChannelID
			else
				return -2
		}

		if !url
			url := (discordMode = 0) ? (webhook "?wait=true") : (this.BaseURL "/channels/" channel "/messages")

		try
		{
			wr := ComObject("WinHttp.WinHttpRequest.5.1")
			wr.Option[9] := 2720
			wr.Open("POST", url, 1)
			if (discordMode = 1)
			{
				wr.SetRequestHeader("User-Agent", "DiscordBot (AHK, " A_AhkVersion ")")
				wr.SetRequestHeader("Authorization", "Bot " bottoken)
			}
			wr.SetRequestHeader("Content-Type", contentType)
			wr.SetTimeouts(0, 60000, 120000, 30000)
			wr.Send(postdata)
			wr.WaitForResponse()
			return wr.ResponseText
		}
	}

	static GetCommands(channel)
	{
		global discordMode, commandPrefix

		if (discordMode = 0)
			return -1

		Loop (n := (messages := this.GetRecentMessages(channel)).Length)
		{
			i := n - A_Index + 1
			(SubStr(content := Trim(messages[i]["content"]), 1, StrLen(commandPrefix)) = commandPrefix) && command_buffer.Push({content:content, id:messages[i]["id"], url:messages[i]["attachments"].Has(1) ? messages[i]["attachments"][1]["url"] : ""})
		}
	}

	static GetRecentMessages(channel)
	{
		global discordMode
		static lastmsg := Map()

		if (discordMode = 0)
			return -1

		try
			(messages := JSON.parse(text := this.GetMessageAPI(lastmsg.Has(channel) ? ("?after=" lastmsg[channel]) : "?limit=1", channel))).Length
		catch
			return []

		if (messages.Has(1))
			lastmsg[channel] := messages[1]["id"]

		return messages
	}

	static GetMessageAPI(params:="", channel:="")
	{
		global bottoken, discordMode, MainChannelCheck, MainChannelID

		if (discordMode = 0)
			return -1

		if !channel
		{
			if (MainChannelCheck = 1)
				channel := MainChannelID
			else
				return -2
		}

		try
		{
			wr := ComObject("WinHttp.WinHttpRequest.5.1")
			wr.Option[9] := 2720
			wr.Open("GET", this.BaseURL "/channels/" channel "/messages" params, 1)
			wr.SetRequestHeader("User-Agent", "DiscordBot (AHK, " A_AhkVersion ")")
			wr.SetRequestHeader("Authorization", "Bot " bottoken)
			wr.SetRequestHeader("Content-Type", "application/json")
			wr.Send()
			wr.WaitForResponse()
			return wr.ResponseText
		}
	}

	static EditMessageAPI(id, postdata, contentType:="application/json", channel:="")
	{
		if (!channel && (discordMode = 1))
		{
			if (MainChannelCheck = 1)
				channel := MainChannelID
			else
				return -2
		}

		url := (discordMode = 0) ? (webhook "/messages/" id) : (this.BaseURL "/channels/" channel "/messages/" id)

		try
		{
			wr := ComObject("WinHttp.WinHttpRequest.5.1")
			wr.Option[9] := 2720
			wr.Open("PATCH", url, 1)
			if (discordMode = 1)
			{
				wr.SetRequestHeader("User-Agent", "DiscordBot (AHK, " A_AhkVersion ")")
				wr.SetRequestHeader("Authorization", "Bot " bottoken)
			}
			wr.SetRequestHeader("Content-Type", contentType)
			wr.SetTimeouts(0, 60000, 120000, 30000)
			wr.Send(postdata)
			wr.WaitForResponse()
			return wr.ResponseText
		}
	}

	static CreateFormData(&retData, &contentType, fields)
	{
		static chars := "0|1|2|3|4|5|6|7|8|9|a|b|c|d|e|f|g|h|i|j|k|l|m|n|o|p|q|r|s|t|u|v|w|x|y|z"

		chars := Sort(chars, "D| Random")
		boundary := SubStr(StrReplace(chars, "|"), 1, 12)
		hData := DllCall("GlobalAlloc", "UInt", 0x2, "UPtr", 0, "Ptr")
		DllCall("ole32\CreateStreamOnHGlobal", "Ptr", hData, "Int", 0, "PtrP", &pStream:=0, "UInt")

		for field in fields
		{
			str :=
			(
			'

			------------------------------' boundary '
			Content-Disposition: form-data; name="' field["name"] '"' (field.Has("filename") ? ('; filename="' field["filename"] '"') : "") '
			Content-Type: ' field["content-type"] '

			' (field.Has("content") ? (field["content"] "`r`n") : "")
			)

			utf8 := Buffer(length := StrPut(str, "UTF-8") - 1), StrPut(str, utf8, length, "UTF-8")
			DllCall("shlwapi\IStream_Write", "Ptr", pStream, "Ptr", utf8.Ptr, "UInt", length, "UInt")

			if field.Has("pBitmap")
			{
				try
				{
					pFileStream := Gdip_SaveBitmapToStream(field["pBitmap"])
					DllCall("shlwapi\IStream_Size", "Ptr", pFileStream, "UInt64P", &size:=0, "UInt")
					DllCall("shlwapi\IStream_Reset", "Ptr", pFileStream, "UInt")
					DllCall("shlwapi\IStream_Copy", "Ptr", pFileStream, "Ptr", pStream, "UInt", size, "UInt")
					ObjRelease(pFileStream)
				}
			}

			if field.Has("file")
			{
				DllCall("shlwapi\SHCreateStreamOnFileEx", "WStr", field["file"], "Int", 0, "UInt", 0x80, "Int", 0, "Ptr", 0, "PtrP", &pFileStream:=0)
				DllCall("shlwapi\IStream_Size", "Ptr", pFileStream, "UInt64P", &size:=0, "UInt")
				DllCall("shlwapi\IStream_Copy", "Ptr", pFileStream, "Ptr", pStream, "UInt", size, "UInt")
				ObjRelease(pFileStream)
			}
		}

		str :=
		(
		'

		------------------------------' boundary '--
		'
		)

		utf8 := Buffer(length := StrPut(str, "UTF-8") - 1), StrPut(str, utf8, length, "UTF-8")
		DllCall("shlwapi\IStream_Write", "Ptr", pStream, "Ptr", utf8.Ptr, "UInt", length, "UInt")
		ObjRelease(pStream)

		pData := DllCall("GlobalLock", "Ptr", hData, "Ptr")
		size := DllCall("GlobalSize", "Ptr", pData, "UPtr")

		retData := ComObjArray(0x11, size)
		pvData := NumGet(ComObjValue(retData), 8 + A_PtrSize, "Ptr")
		DllCall("RtlMoveMemory", "Ptr", pvData, "Ptr", pData, "Ptr", size)

		DllCall("GlobalUnlock", "Ptr", hData)
		DllCall("GlobalFree", "Ptr", hData, "Ptr")
		contentType := "multipart/form-data; boundary=----------------------------" boundary
	}
}

nm_TrimLog(size)
{
	global logsize
	try
	{
		log := FileOpen("settings\debug_log.txt", "r-d"), log.Seek(-((log.Length < size) ? (f := log.Length) : size), 2), txt := log.Read(), log.Close()
		log := FileOpen("settings\debug_log.txt", "w-d"), log.Write(SubStr(txt, f ? 1 : InStr(txt, "`n")+1)), logsize := log.Length, log.Close()
	}
}

nm_setStatus(wParam, lParam, *)
{
	return status_buffer.Push(StrGet(lParam))
}

nm_sendPostData(wParam, lParam, *) ; currently only ReportChannelID
{
	Critical
	global ReportChannelID, MainChannelID
	discord.SendMessageAPI(StrGet(NumGet(lParam + 2*A_PtrSize, "UPtr")), "application/json", (StrLen(ReportChannelID) > 16) ? ReportChannelID : MainChannelID)
	return 0
}

nowUnix() => DateDiff(A_NowUTC, "19700101000000", "Seconds")

UpdateStr(var, value, section)
{
	global
	static sections := Map("Boost",1,"Collect",2,"Gather",3,"Planters",4,"Quests",5,"Settings",6,"Status",7,"Blender",8,"Shrine",9)
	try %var% := value
	IniWrite value, "settings\nm_config.ini", section, var
	DetectHiddenWindows 1
	if WinExist("natro_macro ahk_class AutoHotkey")
		PostMessage 0x5553, settings[var].enum, sections[section]
	if WinExist("background.ahk ahk_class AutoHotkey")
		PostMessage 0x5553, settings[var].enum, sections[section]
}

UpdateInt(var, value, section)
{
	global
	try %var% := value
	IniWrite value, "settings\nm_config.ini", section, var
	DetectHiddenWindows 1
	if WinExist("natro_macro ahk_class AutoHotkey")
		PostMessage 0x5552, settings[var].enum, value
	if WinExist("background.ahk ahk_class AutoHotkey")
		PostMessage 0x5552, settings[var].enum, value
}

nm_setGlobalInt(wParam, lParam, *)
{
	global
	Critical
	local var
	; enumeration
	#Include %A_ScriptDir%\..\lib\enum\EnumInt.ahk

	var := arr[wParam], %var% := lParam
	return 0
}

nm_setGlobalStr(wParam, lParam, *)
{
	global
	Critical
	local var
	; enumeration
	#Include %A_ScriptDir%\..\lib\enum\EnumStr.ahk
	static sections := ["Boost","Collect","Gather","Planters","Quests","Settings","Status","Blender","Shrine"]

	var := arr[wParam], section := sections[lParam]
	%var% := IniRead(A_ScriptDir "\..\settings\nm_config.ini", section, var)
	return 0
}

nm_sendHeartbeat(*)
{
	Critical
	DetectHiddenWindows 1
	if WinExist("Heartbeat.ahk ahk_class AutoHotkey") {
		PostMessage 0x5556, 3
	}
	return 0
}

nm_sendItemPicture(wParam, lParam,*) {
	critical
	switch wParam {
		case 0:
			switch lParam {
				case 0:
					discord.SendEmbed("No Roblox window found!", 16711731)
				case 1:
					discord.SendEmbed("Item was not found.", 16711731)
				case 2:
					discord.SendEmbed("Can't open inventory!", 16711731)
			}
		default:
			GetRobloxClientPos()
			discord.SendEmbed("Item Found!", 5066239, , (pBMScreen := Gdip_BitmapFromScreen(windowX "|" wParam "|306|97")))
			Gdip_DisposeImage(pBMScreen)
	}
}

ExitFunc(*)
{
	Critical
	global status_buffer
	arr := []
	for k,v in status_buffer
		arr.Push(v)
	for k,v in arr
		nm_status(v)
	ExitApp
}

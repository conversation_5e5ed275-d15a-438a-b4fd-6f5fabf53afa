﻿If (HiveBees >= 25) && (MoveMethod = "cannon")
{
    nm_gotoramp()
    nm_gotocannon()
    send "{e down}"
    HyperSleep(100)
    send "{e up}"
    HyperSleep(500)
    send "{" RotRight " 4}{" RightKey " down}"
    HyperSleep(1000)
    send "{space}"
    HyperSleep(500)
    send "{space}"
    HyperSleep(2900)
    send "{" RightKey " up}{" FwdKey " down}{" LeftKey " down}"
    HyperSleep(1600)
    send "{space}"
    HyperSleep(1000)
    nm_Walk(14, <PERSON>wd<PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(10, FwdKey)
    nm_Walk(7, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
}
else {
    nm_gotoramp()
    nm_Walk(67.5, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    send "{" RotRight " 4}"
    nm_Walk(30, FwdKey)
    nm_Walk(20, Fwd<PERSON>ey, <PERSON>K<PERSON>)
    send "{" RotRight " 2}"
    nm_Walk(43.5, Fwd<PERSON><PERSON>)
    nm_Walk(18, <PERSON><PERSON><PERSON>)
    nm_Walk(6, Fwd<PERSON><PERSON>)
    send "{" RotLeft " 2}"
    nm_Walk(66, <PERSON>wd<PERSON><PERSON>)
    nm_Walk(19, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    nm_Walk(7, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey)
}
;path 230212 zaappiix
;path 230729 noobyguy: If (HiveBees < 25) && (MoveMethod = "cannon") & walk path
;path ferox7274: cannon path

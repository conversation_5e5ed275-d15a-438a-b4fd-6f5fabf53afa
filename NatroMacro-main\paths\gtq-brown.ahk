﻿if (MoveMethod = "walk")
{
	nm_gotoramp()
	nm_Walk(44.75, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(42.5, <PERSON><PERSON><PERSON>)
	nm_Walk(8.5, <PERSON><PERSON><PERSON>)
	nm_Walk(22.5, <PERSON><PERSON><PERSON>)
	send "{" RotLeft " 2}"
	nm_Walk(40, Fwd<PERSON><PERSON>)
	nm_Walk(1.2, <PERSON><PERSON><PERSON>)
	nm_Walk(15, <PERSON><PERSON><PERSON>)
}
else
{
	nm_gotoramp()
	nm_gotocannon()
	Send "{e down}"
	HyperSleep(100)
	Send "{e up}{" FwdKey " down}{" LeftKey " down}"
	HyperSleep(1500)
	send "{space 2}"
	Sleep 8000
	Send "{" FwdKey " up}{" LeftKey " up}"
	nm_Walk(20, <PERSON>K<PERSON>)
	nm_Walk(8, <PERSON><PERSON><PERSON>)
	nm_Walk(3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
	nm_Walk(2, <PERSON>K<PERSON>)
}
